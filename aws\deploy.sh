#!/bin/bash

# AWS Lambda TTS Proxy 部署脚本
# 使用 AWS SAM (Serverless Application Model) 进行部署

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${2}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

print_error() {
    print_message "$1" "$RED"
}

print_success() {
    print_message "$1" "$GREEN"
}

print_warning() {
    print_message "$1" "$YELLOW"
}

print_info() {
    print_message "$1" "$BLUE"
}

# 检查必要的工具
check_prerequisites() {
    print_info "检查部署前置条件..."
    
    # 检查 AWS CLI
    if ! command -v aws &> /dev/null; then
        print_error "AWS CLI 未安装。请先安装 AWS CLI。"
        exit 1
    fi
    
    # 检查 SAM CLI
    if ! command -v sam &> /dev/null; then
        print_error "AWS SAM CLI 未安装。请先安装 SAM CLI。"
        print_info "安装命令: pip install aws-sam-cli"
        exit 1
    fi
    
    # 检查 AWS 凭证
    if ! aws sts get-caller-identity &> /dev/null; then
        print_error "AWS 凭证未配置。请运行 'aws configure' 配置凭证。"
        exit 1
    fi
    
    print_success "前置条件检查通过！"
}

# 获取用户输入
get_deployment_params() {
    print_info "配置部署参数..."
    
    # 获取堆栈名称
    read -p "请输入堆栈名称 (默认: tts-proxy): " STACK_NAME
    STACK_NAME=${STACK_NAME:-tts-proxy}
    
    # 获取代理密钥
    while true; do
        read -s -p "请输入代理密钥 (至少8位字符): " PROXY_SECRET
        echo
        if [ ${#PROXY_SECRET} -ge 8 ]; then
            break
        else
            print_error "代理密钥长度必须至少8位字符！"
        fi
    done
    
    # 获取环境
    read -p "请选择环境 (development/production, 默认: production): " ENVIRONMENT
    ENVIRONMENT=${ENVIRONMENT:-production}
    
    # 获取AWS区域
    AWS_REGION=$(aws configure get region)
    read -p "请确认AWS区域 (当前: $AWS_REGION): " NEW_REGION
    if [ ! -z "$NEW_REGION" ]; then
        AWS_REGION=$NEW_REGION
    fi
    
    print_success "部署参数配置完成！"
    print_info "堆栈名称: $STACK_NAME"
    print_info "环境: $ENVIRONMENT"
    print_info "AWS区域: $AWS_REGION"
}

# 构建应用
build_application() {
    print_info "构建应用..."
    
    # 安装依赖
    if [ -f "package.json" ]; then
        print_info "安装 Node.js 依赖..."
        npm install --production
    fi
    
    # 使用 SAM 构建
    print_info "使用 SAM 构建应用..."
    sam build
    
    print_success "应用构建完成！"
}

# 部署应用
deploy_application() {
    print_info "部署应用到 AWS..."
    
    # 首次部署使用 guided 模式
    if ! aws cloudformation describe-stacks --stack-name "$STACK_NAME" --region "$AWS_REGION" &> /dev/null; then
        print_info "首次部署，使用引导模式..."
        sam deploy \
            --guided \
            --stack-name "$STACK_NAME" \
            --region "$AWS_REGION" \
            --parameter-overrides \
                ProxySecret="$PROXY_SECRET" \
                Environment="$ENVIRONMENT" \
            --capabilities CAPABILITY_IAM \
            --confirm-changeset
    else
        print_info "更新现有堆栈..."
        sam deploy \
            --stack-name "$STACK_NAME" \
            --region "$AWS_REGION" \
            --parameter-overrides \
                ProxySecret="$PROXY_SECRET" \
                Environment="$ENVIRONMENT" \
            --capabilities CAPABILITY_IAM \
            --no-confirm-changeset
    fi
    
    print_success "应用部署完成！"
}

# 获取部署信息
get_deployment_info() {
    print_info "获取部署信息..."
    
    # 获取 API Gateway URL
    API_URL=$(aws cloudformation describe-stacks \
        --stack-name "$STACK_NAME" \
        --region "$AWS_REGION" \
        --query 'Stacks[0].Outputs[?OutputKey==`TtsProxyApiUrl`].OutputValue' \
        --output text)
    
    # 获取健康检查端点
    HEALTH_URL=$(aws cloudformation describe-stacks \
        --stack-name "$STACK_NAME" \
        --region "$AWS_REGION" \
        --query 'Stacks[0].Outputs[?OutputKey==`HealthEndpoint`].OutputValue' \
        --output text)
    
    print_success "部署信息："
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo "🌐 API Gateway URL: $API_URL"
    echo "❤️  健康检查端点: $HEALTH_URL"
    echo "🎤 TTS端点: $API_URL/api/v1/text-to-speech/{voice_id}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
}

# 测试部署
test_deployment() {
    print_info "测试部署..."
    
    if [ ! -z "$HEALTH_URL" ]; then
        print_info "测试健康检查端点..."
        
        # 测试健康检查（需要代理密钥）
        HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" \
            -H "x-proxy-secret: $PROXY_SECRET" \
            "$HEALTH_URL")
        
        if [ "$HTTP_STATUS" = "200" ]; then
            print_success "健康检查测试通过！"
        else
            print_warning "健康检查测试失败，HTTP状态码: $HTTP_STATUS"
        fi
    fi
}

# 主函数
main() {
    print_info "开始 AWS Lambda TTS Proxy 部署流程..."
    
    check_prerequisites
    get_deployment_params
    build_application
    deploy_application
    get_deployment_info
    test_deployment
    
    print_success "🎉 部署完成！"
    print_info "请保存上述端点信息，并确保在客户端请求中包含正确的 x-proxy-secret 头。"
}

# 运行主函数
main "$@"
