# AWS Lambda TTS Proxy Deployment Script

param(
    [string]$StackName = "tts-proxy",
    [string]$ProxySecret = "",
    [string]$Environment = "production", 
    [string]$Region = ""
)

Write-Host "================================================" -ForegroundColor Green
Write-Host "AWS Lambda TTS Proxy Deployment Script" -ForegroundColor Green
Write-Host "================================================" -ForegroundColor Green

# Check AWS CLI
Write-Host "[INFO] Checking AWS CLI..." -ForegroundColor Cyan
try {
    $awsVersion = aws --version 2>$null
    if ($awsVersion) {
        Write-Host "[SUCCESS] AWS CLI is installed" -ForegroundColor Green
    } else {
        Write-Host "[ERROR] AWS CLI is not installed" -ForegroundColor Red
        Write-Host "Please download from https://aws.amazon.com/cli/" -ForegroundColor Yellow
        exit 1
    }
} catch {
    Write-Host "[ERROR] AWS CLI check failed" -ForegroundColor Red
    exit 1
}

# Check SAM CLI
Write-Host "[INFO] Checking SAM CLI..." -ForegroundColor Cyan
try {
    $samVersion = sam --version 2>$null
    if ($samVersion) {
        Write-Host "[SUCCESS] SAM CLI is installed" -ForegroundColor Green
    } else {
        Write-Host "[ERROR] SAM CLI is not installed" -ForegroundColor Red
        Write-Host "Please run: pip install aws-sam-cli" -ForegroundColor Yellow
        exit 1
    }
} catch {
    Write-Host "[ERROR] SAM CLI check failed" -ForegroundColor Red
    exit 1
}

# Check Node.js
Write-Host "[INFO] Checking Node.js..." -ForegroundColor Cyan
try {
    $nodeVersion = node --version 2>$null
    if ($nodeVersion) {
        Write-Host "[SUCCESS] Node.js is installed: $nodeVersion" -ForegroundColor Green
    } else {
        Write-Host "[ERROR] Node.js is not installed" -ForegroundColor Red
        Write-Host "Please download from https://nodejs.org/" -ForegroundColor Yellow
        exit 1
    }
} catch {
    Write-Host "[ERROR] Node.js check failed" -ForegroundColor Red
    exit 1
}

# Check AWS credentials
Write-Host "[INFO] Checking AWS credentials..." -ForegroundColor Cyan
try {
    $identity = aws sts get-caller-identity 2>$null | ConvertFrom-Json
    if ($identity) {
        Write-Host "[SUCCESS] AWS credentials are configured" -ForegroundColor Green
        Write-Host "Account ID: $($identity.Account)" -ForegroundColor Cyan
    } else {
        Write-Host "[ERROR] AWS credentials are not configured" -ForegroundColor Red
        Write-Host "Please run: aws configure" -ForegroundColor Yellow
        exit 1
    }
} catch {
    Write-Host "[ERROR] AWS credentials verification failed" -ForegroundColor Red
    Write-Host "Please run: aws configure" -ForegroundColor Yellow
    exit 1
}

Write-Host "`n[SUCCESS] Prerequisites check passed!" -ForegroundColor Green

# Get user input
Write-Host "`n[INFO] Configuring deployment parameters..." -ForegroundColor Cyan

# Stack name
if (-not $StackName) {
    $input = Read-Host "Enter stack name (default: tts-proxy)"
    if ($input) { $StackName = $input } else { $StackName = "tts-proxy" }
}

# Proxy secret
while (-not $ProxySecret -or $ProxySecret.Length -lt 8) {
    $ProxySecret = Read-Host "Enter proxy secret (at least 8 characters)" -AsSecureString
    $ProxySecret = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($ProxySecret))
    if ($ProxySecret.Length -lt 8) {
        Write-Host "[ERROR] Proxy secret must be at least 8 characters!" -ForegroundColor Red
    }
}

# Environment
if (-not $Environment) {
    $input = Read-Host "Select environment (development/production, default: production)"
    if ($input) { $Environment = $input } else { $Environment = "production" }
}

# AWS region
if (-not $Region) {
    try {
        $currentRegion = aws configure get region 2>$null
        if (-not $currentRegion) { $currentRegion = "us-east-1" }
        $input = Read-Host "Confirm AWS region (current: $currentRegion)"
        if ($input) { $Region = $input } else { $Region = $currentRegion }
    } catch {
        $input = Read-Host "Enter AWS region (default: us-east-1)"
        if ($input) { $Region = $input } else { $Region = "us-east-1" }
    }
}

Write-Host "`n[SUCCESS] Deployment parameters configured!" -ForegroundColor Green
Write-Host "Stack name: $StackName" -ForegroundColor Cyan
Write-Host "Environment: $Environment" -ForegroundColor Cyan
Write-Host "AWS region: $Region" -ForegroundColor Cyan

# Install dependencies
Write-Host "`n[INFO] Installing Node.js dependencies..." -ForegroundColor Cyan
if (Test-Path "package.json") {
    npm install --production
    if ($LASTEXITCODE -ne 0) {
        Write-Host "[ERROR] npm install failed" -ForegroundColor Red
        exit 1
    }
    Write-Host "[SUCCESS] Dependencies installed" -ForegroundColor Green
}

# SAM build
Write-Host "`n[INFO] Building application with SAM..." -ForegroundColor Cyan
sam build
if ($LASTEXITCODE -ne 0) {
    Write-Host "[ERROR] SAM build failed" -ForegroundColor Red
    exit 1
}
Write-Host "[SUCCESS] Application built successfully" -ForegroundColor Green

# Check if stack exists
Write-Host "`n[INFO] Checking existing stack..." -ForegroundColor Cyan
$stackExists = $false
try {
    aws cloudformation describe-stacks --stack-name $StackName --region $Region 2>$null | Out-Null
    $stackExists = $true
    Write-Host "[INFO] Found existing stack, will update" -ForegroundColor Yellow
} catch {
    Write-Host "[INFO] No existing stack found, will create new" -ForegroundColor Cyan
}

# Deploy
Write-Host "`n[INFO] Deploying application to AWS..." -ForegroundColor Cyan
if (-not $stackExists) {
    Write-Host "[INFO] First deployment, using guided mode..." -ForegroundColor Cyan
    sam deploy --guided --stack-name $StackName --region $Region --parameter-overrides "ProxySecret=$ProxySecret" "Environment=$Environment" --capabilities CAPABILITY_IAM
} else {
    Write-Host "[INFO] Updating existing stack..." -ForegroundColor Cyan
    sam deploy --stack-name $StackName --region $Region --parameter-overrides "ProxySecret=$ProxySecret" "Environment=$Environment" --capabilities CAPABILITY_IAM --no-confirm-changeset
}

if ($LASTEXITCODE -ne 0) {
    Write-Host "[ERROR] Deployment failed" -ForegroundColor Red
    exit 1
}

Write-Host "`n[SUCCESS] Deployment completed!" -ForegroundColor Green

# Get deployment info
Write-Host "`n[INFO] Getting deployment information..." -ForegroundColor Cyan
try {
    $apiUrl = aws cloudformation describe-stacks --stack-name $StackName --region $Region --query 'Stacks[0].Outputs[?OutputKey==`TtsProxyApiUrl`].OutputValue' --output text 2>$null
    $healthUrl = aws cloudformation describe-stacks --stack-name $StackName --region $Region --query 'Stacks[0].Outputs[?OutputKey==`HealthEndpoint`].OutputValue' --output text 2>$null
    
    Write-Host "`n================================================" -ForegroundColor White
    Write-Host "Deployment Information:" -ForegroundColor Green
    Write-Host "================================================" -ForegroundColor White
    Write-Host "API Gateway URL: $apiUrl" -ForegroundColor Cyan
    Write-Host "Health Check Endpoint: $healthUrl" -ForegroundColor Green
    Write-Host "TTS Endpoint: $apiUrl/api/v1/text-to-speech/{voice_id}" -ForegroundColor Yellow
    Write-Host "================================================" -ForegroundColor White
    
    # Generate config file
    $configLines = @(
        "# AWS Lambda TTS Proxy Configuration",
        "# Generated: $(Get-Date)",
        "",
        "API_BASE_URL=$apiUrl",
        "HEALTH_ENDPOINT=$healthUrl", 
        "TTS_ENDPOINT=$apiUrl/api/v1/text-to-speech/{voice_id}",
        "PROXY_SECRET=$ProxySecret",
        "STACK_NAME=$StackName",
        "REGION=$Region",
        "ENVIRONMENT=$Environment"
    )
    
    $configLines | Out-File -FilePath "deployment-config.txt" -Encoding UTF8
    Write-Host "`n[INFO] Configuration saved to: deployment-config.txt" -ForegroundColor Cyan
    
} catch {
    Write-Host "[WARNING] Unable to get deployment info, please check stack status" -ForegroundColor Yellow
}

Write-Host "`n[SUCCESS] Deployment completed successfully!" -ForegroundColor Green
Write-Host "Please save the endpoint information above and ensure to include the correct x-proxy-secret header in client requests." -ForegroundColor Cyan
