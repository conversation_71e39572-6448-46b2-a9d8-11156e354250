@echo off
REM AWS Lambda TTS Proxy Windows批处理部署脚本
REM 这是一个简化版本，用于不支持PowerShell的环境

setlocal enabledelayedexpansion

echo ================================================
echo AWS Lambda TTS Proxy 部署脚本 (Windows)
echo ================================================

REM 检查必要工具
echo [INFO] 检查部署前置条件...

REM 检查 AWS CLI
aws --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] AWS CLI 未安装。请先安装 AWS CLI。
    echo 下载地址: https://aws.amazon.com/cli/
    pause
    exit /b 1
)
echo [OK] AWS CLI 已安装

REM 检查 SAM CLI
sam --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] AWS SAM CLI 未安装。请先安装 SAM CLI。
    echo 安装命令: pip install aws-sam-cli
    echo 或下载MSI: https://github.com/aws/aws-sam-cli/releases
    pause
    exit /b 1
)
echo [OK] SAM CLI 已安装

REM 检查 Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js 未安装。请先安装 Node.js 18+。
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)
echo [OK] Node.js 已安装

REM 检查 AWS 凭证
aws sts get-caller-identity >nul 2>&1
if errorlevel 1 (
    echo [ERROR] AWS 凭证未配置。请运行 'aws configure' 配置凭证。
    pause
    exit /b 1
)
echo [OK] AWS 凭证已配置

echo.
echo [INFO] 前置条件检查通过！
echo.

REM 获取用户输入
set /p STACK_NAME="请输入堆栈名称 (默认: tts-proxy): "
if "%STACK_NAME%"=="" set STACK_NAME=tts-proxy

:input_secret
set /p PROXY_SECRET="请输入代理密钥 (至少8位字符): "
if "%PROXY_SECRET%"=="" (
    echo [ERROR] 代理密钥不能为空！
    goto input_secret
)
REM 简单长度检查（批处理中字符串长度检查比较复杂）
echo %PROXY_SECRET%| findstr /R "^........" >nul
if errorlevel 1 (
    echo [ERROR] 代理密钥长度必须至少8位字符！
    goto input_secret
)

set /p ENVIRONMENT="请选择环境 (development/production, 默认: production): "
if "%ENVIRONMENT%"=="" set ENVIRONMENT=production

REM 获取AWS区域
for /f "tokens=*" %%i in ('aws configure get region 2^>nul') do set CURRENT_REGION=%%i
if "%CURRENT_REGION%"=="" set CURRENT_REGION=us-east-1
set /p AWS_REGION="请确认AWS区域 (当前: %CURRENT_REGION%): "
if "%AWS_REGION%"=="" set AWS_REGION=%CURRENT_REGION%

echo.
echo [INFO] 部署参数配置完成！
echo 堆栈名称: %STACK_NAME%
echo 环境: %ENVIRONMENT%
echo AWS区域: %AWS_REGION%
echo.

REM 安装依赖
if exist package.json (
    echo [INFO] 安装 Node.js 依赖...
    npm install --production
    if errorlevel 1 (
        echo [ERROR] npm install 失败
        pause
        exit /b 1
    )
)

REM 构建应用
echo [INFO] 使用 SAM 构建应用...
sam build
if errorlevel 1 (
    echo [ERROR] SAM build 失败
    pause
    exit /b 1
)

echo [OK] 应用构建完成！

REM 检查堆栈是否存在
aws cloudformation describe-stacks --stack-name %STACK_NAME% --region %AWS_REGION% >nul 2>&1
if errorlevel 1 (
    echo [INFO] 首次部署，使用引导模式...
    sam deploy --guided --stack-name %STACK_NAME% --region %AWS_REGION% --parameter-overrides ProxySecret=%PROXY_SECRET% Environment=%ENVIRONMENT% --capabilities CAPABILITY_IAM
) else (
    echo [INFO] 更新现有堆栈...
    sam deploy --stack-name %STACK_NAME% --region %AWS_REGION% --parameter-overrides ProxySecret=%PROXY_SECRET% Environment=%ENVIRONMENT% --capabilities CAPABILITY_IAM --no-confirm-changeset
)

if errorlevel 1 (
    echo [ERROR] 部署失败
    pause
    exit /b 1
)

echo [OK] 应用部署完成！

REM 获取部署信息
echo [INFO] 获取部署信息...
for /f "tokens=*" %%i in ('aws cloudformation describe-stacks --stack-name %STACK_NAME% --region %AWS_REGION% --query "Stacks[0].Outputs[?OutputKey==`TtsProxyApiUrl`].OutputValue" --output text 2^>nul') do set API_URL=%%i
for /f "tokens=*" %%i in ('aws cloudformation describe-stacks --stack-name %STACK_NAME% --region %AWS_REGION% --query "Stacks[0].Outputs[?OutputKey==`HealthEndpoint`].OutputValue" --output text 2^>nul') do set HEALTH_URL=%%i

echo.
echo ================================================
echo 部署信息:
echo ================================================
echo API Gateway URL: %API_URL%
echo 健康检查端点: %HEALTH_URL%
echo TTS端点: %API_URL%/api/v1/text-to-speech/{voice_id}
echo ================================================

REM 生成配置文件
echo # AWS Lambda TTS Proxy 配置信息 > deployment-config.txt
echo # 生成时间: %date% %time% >> deployment-config.txt
echo. >> deployment-config.txt
echo API_BASE_URL=%API_URL% >> deployment-config.txt
echo HEALTH_ENDPOINT=%HEALTH_URL% >> deployment-config.txt
echo TTS_ENDPOINT=%API_URL%/api/v1/text-to-speech/{voice_id} >> deployment-config.txt
echo PROXY_SECRET=%PROXY_SECRET% >> deployment-config.txt
echo STACK_NAME=%STACK_NAME% >> deployment-config.txt
echo REGION=%AWS_REGION% >> deployment-config.txt
echo ENVIRONMENT=%ENVIRONMENT% >> deployment-config.txt

echo.
echo [OK] 部署完成！配置信息已保存到: deployment-config.txt
echo 请保存上述端点信息，并确保在客户端请求中包含正确的 x-proxy-secret 头。
echo.
pause
