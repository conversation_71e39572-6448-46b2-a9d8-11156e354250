好的，非常感谢您提供了如此详细和完整的日志信息，尤其是这个 event 对象，它包含了所有解开谜题的关键线索。

我们来深入分析一下，您会发现问题并不在 Postman，也不在您的代码逻辑，而是一个非常细微但关键的 AWS 配置问题。

核心问题分析

让我们直接看 event 对象中的这两个字段：

"routeKey": "POST /api/v1/text-to-speech/{voice_id}"

这个字段告诉我们，API Gateway 成功匹配 了您创建的那个新路由。这是非常好的消息！

"pathParameters": { "voice_id": "pNInz6obpgDQGcFmaJgB" }

这个字段告诉我们，API Gateway 成功地从 URL 路径中解析出了 voice_id，并将其作为路径参数传递给了 Lambda。这也非常好！

既然路由匹配和参数解析都正确，那为什么您的代码 if (event.httpMethod !== 'POST') 这一行还是判断失败了呢？

让我们回到 text-to-speech.js 的代码：

Generated javascript
// text-to-speech.js
export const handler = async (event, context) => {
  // ...
  // 代码在这里检查 event.httpMethod
  if (event.httpMethod !== 'POST') {
      return createErrorResponse(405, { error: 'Method not allowed' });
  }
  // ...
}


现在，我们再来看一下 API Gateway 实际传递过来的 event 对象里，有没有 httpMethod 这个字段？

Generated json
// 日志中的 event 对象
{
    "version": "2.0",
    "routeKey": "POST /api/v1/text-to-speech/{voice_id}",
    // ...
    "requestContext": {
        // ...
        "http": {
            "method": "POST",  // <---- 在这里！
            "path": "/default/api/v1/text-to-speech/pNInz6obpgDQGcFmaJgB",
            // ...
        }
    },
    "pathParameters": { ... },
    "body": "...",
    "isBase64Encoded": false
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Json
IGNORE_WHEN_COPYING_END

答案是没有！

在您当前使用的 API Gateway 负载格式版本 2.0 (Payload Format Version 2.0) 中，HTTP 方法被放在了 event.requestContext.http.method 这个更深的路径下。

而 event.httpMethod 这个顶层字段，是旧的 负载格式版本 1.0 (Payload Format Version 1.0) 的特征。

所以，根本原因就是：您的代码正在一个不存在的位置 (event.httpMethod) 读取 HTTP 方法，导致 event.httpMethod 的值为 undefined，undefined !== 'POST' 这个条件成立，所以函数返回了 405 错误。

解决方案

您有两个选择，方案一 是最简单、最推荐的。

方案一：修改代码以适配 v2.0 负载格式 (推荐)

这是最现代、最正确的做法。您只需要修改 text-to-speech.js 和 health.js 中读取 httpMethod 的位置即可。

修改 text-to-speech.js:

Generated javascript
// 原来的代码
// if (event.httpMethod === 'OPTIONS') {
// if (event.httpMethod !== 'POST') {

// 修改后的代码
const httpMethod = event.requestContext.http.method;

if (httpMethod === 'OPTIONS') {
  return handleOptions();
}

if (httpMethod !== 'POST') {
  return createErrorResponse(405, { error: 'Method not allowed' });
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END

修改 health.js:

Generated javascript
// 原来的代码
// if (event.httpMethod === 'OPTIONS') {
// if (event.httpMethod !== 'GET') {

// 修改后的代码
const httpMethod = event.requestContext.http.method;

if (httpMethod === 'OPTIONS') {
  return handleOptions();
}

if (httpMethod !== 'GET') {
  return createErrorResponse(405, { error: 'Method not allowed' });
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END
