# Windows 系统部署指南

## 🚀 Windows 快速部署

### 1. 前置要求安装

#### 安装 AWS CLI
1. 下载 AWS CLI MSI 安装包：https://aws.amazon.com/cli/
2. 运行安装程序，按默认设置安装
3. 打开命令提示符，验证安装：
   ```cmd
   aws --version
   ```

#### 安装 AWS SAM CLI
**方式1: 使用 MSI 安装包 (推荐)**
1. 下载 SAM CLI MSI：https://github.com/aws/aws-sam-cli/releases
2. 运行安装程序

**方式2: 使用 pip 安装**
```cmd
pip install aws-sam-cli
```

#### 安装 Node.js
1. 下载 Node.js LTS 版本：https://nodejs.org/
2. 运行安装程序，确保选择 "Add to PATH"
3. 验证安装：
   ```cmd
   node --version
   npm --version
   ```

### 2. 配置 AWS 凭证

```cmd
aws configure
```

输入你的：
- AWS Access Key ID
- AWS Secret Access Key  
- Default region (例如: us-east-1)
- Default output format (建议: json)

### 3. 部署应用

#### 方式1: PowerShell 脚本 (推荐)

1. 打开 PowerShell (以管理员身份运行)
2. 导航到项目目录：
   ```powershell
   cd aws
   ```
3. 运行部署脚本：
   ```powershell
   .\deploy.ps1
   ```

#### 方式2: 批处理脚本

1. 打开命令提示符 (以管理员身份运行)
2. 导航到项目目录：
   ```cmd
   cd aws
   ```
3. 运行部署脚本：
   ```cmd
   deploy.bat
   ```

#### 方式3: 手动部署

```cmd
cd aws
npm install
sam build
sam deploy --guided
```

## 🔧 常见问题解决

### 问题1: PowerShell 执行策略错误

**错误信息:**
```
无法加载文件 deploy.ps1，因为在此系统上禁止运行脚本
```

**解决方案:**
```powershell
# 临时允许脚本执行
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# 运行部署脚本
.\deploy.ps1

# 恢复执行策略 (可选)
Set-ExecutionPolicy -ExecutionPolicy Restricted -Scope CurrentUser
```

### 问题2: AWS CLI 未找到

**错误信息:**
```
'aws' 不是内部或外部命令
```

**解决方案:**
1. 重新安装 AWS CLI，确保选择 "Add to PATH"
2. 重启命令提示符/PowerShell
3. 手动添加到 PATH：
   - 默认安装路径：`C:\Program Files\Amazon\AWSCLIV2\`
   - 添加到系统环境变量 PATH

### 问题3: SAM CLI 未找到

**错误信息:**
```
'sam' 不是内部或外部命令
```

**解决方案:**
1. 确认 SAM CLI 已正确安装
2. 重启命令提示符/PowerShell
3. 如果使用 pip 安装，确保 Python Scripts 目录在 PATH 中

### 问题4: Node.js 版本过低

**错误信息:**
```
Node.js version 14.x is not supported
```

**解决方案:**
1. 卸载旧版本 Node.js
2. 下载并安装 Node.js 18+ LTS 版本
3. 重启命令提示符/PowerShell

### 问题5: npm install 失败

**错误信息:**
```
npm ERR! network timeout
```

**解决方案:**
```cmd
# 设置 npm 镜像源
npm config set registry https://registry.npmmirror.com

# 或者使用代理
npm config set proxy http://proxy-server:port
npm config set https-proxy http://proxy-server:port

# 重新安装
npm install
```

### 问题6: AWS 凭证配置错误

**错误信息:**
```
Unable to locate credentials
```

**解决方案:**
1. 重新运行 `aws configure`
2. 确认 Access Key 和 Secret Key 正确
3. 检查 IAM 用户权限

### 问题7: 部署权限不足

**错误信息:**
```
User is not authorized to perform: cloudformation:CreateStack
```

**解决方案:**
确保 IAM 用户具有以下权限：
- CloudFormation 完整权限
- Lambda 完整权限
- API Gateway 完整权限
- DynamoDB 完整权限
- IAM 角色创建权限

## 📋 部署后验证

### 1. 检查部署状态

```cmd
aws cloudformation describe-stacks --stack-name tts-proxy
```

### 2. 测试健康检查

```cmd
curl -H "x-proxy-secret: your-secret" https://your-api.execute-api.us-east-1.amazonaws.com/v1/health
```

### 3. 查看日志

```cmd
sam logs -n HealthFunction --stack-name tts-proxy --tail
```

## 🛠️ 开发环境设置

### 推荐的 Windows 开发工具

1. **Windows Terminal** - 现代化终端
2. **PowerShell 7** - 最新版 PowerShell
3. **Visual Studio Code** - 代码编辑器
4. **Git for Windows** - 版本控制

### VS Code 扩展推荐

- AWS Toolkit
- PowerShell
- JavaScript (ES6) code snippets
- REST Client

## 🔄 更新和维护

### 更新代码

```cmd
cd aws
sam build
sam deploy
```

### 查看资源

```cmd
aws cloudformation list-stack-resources --stack-name tts-proxy
```

### 删除堆栈

```cmd
aws cloudformation delete-stack --stack-name tts-proxy
```

## 💡 提示和技巧

1. **使用 PowerShell ISE** 进行脚本调试
2. **设置 AWS CLI 配置文件** 管理多个账户
3. **使用 SAM local** 进行本地测试
4. **启用 CloudTrail** 监控 API 调用
5. **设置 CloudWatch 告警** 监控成本和性能

## 📞 获取帮助

如果遇到问题：

1. 查看 `deployment-config.txt` 文件中的配置信息
2. 检查 CloudWatch 日志
3. 参考 AWS 官方文档
4. 在项目 Issues 中提问

---

**注意**: Windows 系统建议使用 PowerShell 脚本进行部署，功能更完整且错误处理更好。
