import { 
  checkProxySecret, 
  handleOptions, 
  createErrorResponse, 
  createSuccessResponse 
} from '../lib/utils.js';

// 【AWS Lambda适配】健康检查函数
export const handler = async (event, context) => {
  console.log('[HEALTH] Lambda function invoked');
  console.log('[HEALTH] Event:', JSON.stringify(event, null, 2));

  try {
    // 获取HTTP方法 - 兼容v1.0和v2.0 payload format
    const httpMethod = event.httpMethod || event.requestContext?.http?.method;

    // 处理 CORS 预检请求
    if (httpMethod === 'OPTIONS') {
      return handleOptions();
    }

    // 只允许 GET 请求
    if (httpMethod !== 'GET') {
      return createErrorResponse(405, { error: 'Method not allowed' });
    }

    // 【安全验证】检查代理密钥
    const authResult = checkProxySecret(event.headers);
    if (!authResult.isValid) {
      return createErrorResponse(401, authResult.error);
    }

    // 返回健康状态
    const healthData = {
      status: 'healthy',
      message: 'Proxy server is up and running on AWS Lambda',
      timestamp: new Date().toISOString(),
      platform: 'aws-lambda',
      region: process.env.AWS_REGION || 'unknown',
      functionName: context.functionName || 'unknown',
      requestId: context.awsRequestId || 'unknown'
    };

    console.log('[HEALTH] Returning healthy status');
    return createSuccessResponse(healthData);

  } catch (error) {
    console.error('[HEALTH] Error:', error);
    return createErrorResponse(500, {
      error: 'Internal server error',
      details: error.message
    });
  }
};
