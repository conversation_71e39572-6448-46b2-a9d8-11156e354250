# AWS Lambda TTS Proxy 部署指南

## 🚀 快速开始

### 1. 前置要求检查

确保你已经安装并配置了以下工具：

```bash
# 检查 AWS CLI
aws --version
aws sts get-caller-identity

# 检查 SAM CLI  
sam --version

# 检查 Node.js
node --version  # 需要 18+
npm --version
```

### 2. 克隆并进入项目

```bash
cd aws/
npm install
```

### 3. 一键部署

**Windows:**
```powershell
.\deploy.sh
```

**Linux/macOS:**
```bash
chmod +x deploy.sh
./deploy.sh
```

部署脚本会引导你完成所有配置。

## 📋 手动部署步骤

如果你更喜欢手动控制每个步骤：

### 1. 构建项目

```bash
sam build
```

### 2. 首次部署

```bash
sam deploy --guided
```

按提示输入：
- **Stack Name**: `tts-proxy` (或你喜欢的名称)
- **AWS Region**: `us-east-1` (或你的首选区域)
- **ProxySecret**: 至少8位字符的密钥
- **Environment**: `production`
- **Confirm changes**: `Y`
- **Allow SAM to create IAM roles**: `Y`
- **Save parameters to samconfig.toml**: `Y`

### 3. 后续更新

```bash
sam build && sam deploy
```

## 🔧 部署后配置

### 1. 获取API端点

部署完成后，记录输出的端点信息：

```
Outputs:
TtsProxyApiUrl: https://abc123.execute-api.us-east-1.amazonaws.com/v1
HealthEndpoint: https://abc123.execute-api.us-east-1.amazonaws.com/v1/health
```

### 2. 测试部署

编辑 `examples/client-test.js`，更新配置：

```javascript
const CONFIG = {
  baseUrl: 'https://your-actual-api-id.execute-api.us-east-1.amazonaws.com/v1',
  proxySecret: 'your-actual-proxy-secret',
  voiceId: 'EXAVITQu4vr4xnSDxMaL'
};
```

运行测试：

```bash
cd examples/
npm install node-fetch
node client-test.js
```

### 3. 验证功能

**健康检查:**
```bash
curl -H "x-proxy-secret: your-secret" \
  https://your-api.execute-api.us-east-1.amazonaws.com/v1/health
```

**TTS测试:**
```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -H "x-proxy-secret: your-secret" \
  -d '{"text":"Hello world","model_id":"eleven_monolingual_v1"}' \
  https://your-api.execute-api.us-east-1.amazonaws.com/v1/api/v1/text-to-speech/EXAVITQu4vr4xnSDxMaL \
  --output test.mp3
```

## 🔍 监控和调试

### 1. 查看日志

```bash
# 健康检查函数日志
sam logs -n HealthFunction --stack-name tts-proxy --tail

# TTS函数日志  
sam logs -n TextToSpeechFunction --stack-name tts-proxy --tail

# 或使用AWS CLI
aws logs tail /aws/lambda/tts-proxy-health --follow
aws logs tail /aws/lambda/tts-proxy-tts --follow
```

### 2. 监控指标

在AWS控制台中查看：
- **Lambda**: 函数调用次数、错误率、执行时间
- **API Gateway**: 请求数量、延迟、错误
- **DynamoDB**: 读写请求、限制情况

### 3. 常见问题排查

**问题1: 部署失败**
```bash
# 查看CloudFormation事件
aws cloudformation describe-stack-events --stack-name tts-proxy
```

**问题2: 函数超时**
- 检查ElevenLabs API响应时间
- 增加Lambda超时设置
- 查看CloudWatch日志

**问题3: DynamoDB权限错误**
- 确认IAM角色有DynamoDB权限
- 检查表名配置是否正确

**问题4: CORS错误**
- 确认API Gateway CORS配置
- 检查预检请求处理

## 🔄 更新和维护

### 1. 更新代码

```bash
# 修改代码后
sam build && sam deploy
```

### 2. 更新配置

```bash
# 更新环境变量
sam deploy --parameter-overrides ProxySecret=new-secret Environment=production
```

### 3. 扩展配置

编辑 `template.yaml` 来调整：
- 函数内存和超时
- DynamoDB配置
- API Gateway设置
- 监控和告警

### 4. 备份和恢复

```bash
# 导出模板
aws cloudformation get-template --stack-name tts-proxy > backup-template.json

# 导出参数
aws cloudformation describe-stacks --stack-name tts-proxy > backup-params.json
```

## 🗑️ 清理资源

### 删除整个堆栈

```bash
aws cloudformation delete-stack --stack-name tts-proxy
```

### 手动清理（如果需要）

```bash
# 删除S3存储桶（如果有）
aws s3 rb s3://sam-deployment-bucket --force

# 删除日志组
aws logs delete-log-group --log-group-name /aws/lambda/tts-proxy-health
aws logs delete-log-group --log-group-name /aws/lambda/tts-proxy-tts
```

## 💰 成本优化

### 1. 监控使用量

- 设置CloudWatch计费告警
- 定期检查AWS Cost Explorer
- 监控Lambda调用次数和执行时间

### 2. 优化配置

```yaml
# 在 template.yaml 中调整
MemorySize: 512  # 根据实际需要调整
Timeout: 30      # 根据API响应时间调整
```

### 3. 预留并发（可选）

对于高流量场景，考虑配置预留并发：

```yaml
ReservedConcurrencyConfiguration:
  ReservedConcurrency: 10
```

## 🔒 安全最佳实践

### 1. 密钥管理

- 使用AWS Secrets Manager存储敏感信息
- 定期轮换代理密钥
- 不要在代码中硬编码密钥

### 2. 网络安全

- 考虑使用VPC端点
- 配置WAF规则（如果需要）
- 启用API Gateway访问日志

### 3. 监控和告警

```bash
# 设置异常告警
aws cloudwatch put-metric-alarm \
  --alarm-name "TTS-High-Error-Rate" \
  --alarm-description "TTS function error rate too high" \
  --metric-name Errors \
  --namespace AWS/Lambda \
  --statistic Sum \
  --period 300 \
  --threshold 10 \
  --comparison-operator GreaterThanThreshold
```

## 📞 获取帮助

如果遇到问题：

1. **查看日志**: CloudWatch Logs 是最好的调试工具
2. **检查文档**: AWS Lambda 和 API Gateway 官方文档
3. **社区支持**: AWS 开发者论坛
4. **专业支持**: AWS Support（如果有支持计划）

---

**提示**: 部署成功后，建议先在测试环境验证所有功能，然后再在生产环境中使用。
