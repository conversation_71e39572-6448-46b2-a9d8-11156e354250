# Vercel TTS Proxy Server

这是为 Cloudflare Worker TTS 服务设计的 Vercel Serverless 代理服务器，与 Railway 版本保持功能一致。

## 🏗️ 架构设计

### Vercel Serverless Functions 架构
- **健康检查**: `/api/health.js` - GET 请求
- **TTS 代理**: `/api/v1/text-to-speech/[voice_id].js` - POST 请求
- **共享工具**: `/lib/utils.js` - 通用函数库

### 与 Railway 版本的差异

| 特性 | Railway 版本 | Vercel 版本 |
|------|-------------|-------------|
| 架构模式 | Express 长期运行服务器 | Serverless Functions |
| 流处理 | 实时流式传输 | 缓冲后发送 |
| 超时时间 | 60秒 | 30秒 |
| 部署方式 | 容器化部署 | 函数即服务 |
| 冷启动 | 无 | 有（首次请求较慢） |

## 🔧 核心功能保持一致

### 1. 安全认证
- 支持 `x-proxy-secret` 头验证
- 可通过环境变量配置
- 调试模式下提供详细日志

### 2. 错误处理
- 完整的上游错误转发
- 网络错误捕获和处理
- 超时错误特殊处理
- 详细的错误日志

### 3. 流式响应
- **优化策略**: 使用 Buffer 收集数据后发送
- **原因**: Serverless 环境对实时流的支持有限
- **效果**: 保证音频完整性，略增延迟

### 4. CORS 支持
- 完整的跨域请求支持
- OPTIONS 预检请求处理
- 灵活的头部配置

## 📦 部署指南

### 1. 环境准备
```bash
# 安装 Vercel CLI
npm i -g vercel

# 安装依赖
npm install
```

### 2. 环境变量配置
在 Vercel 控制面板或使用 CLI 配置：
```bash
# 设置代理密钥
vercel env add PROXY_SECRET

# 设置开发环境（可选）
vercel env add NODE_ENV development
```

### 3. 部署到 Vercel
```bash
# 开发环境测试
vercel dev

# 部署到生产环境
vercel --prod
```

## 🧪 测试验证

### 健康检查
```bash
curl https://your-vercel-app.vercel.app/api/health
```

### TTS 功能测试
```bash
curl -X POST https://your-vercel-app.vercel.app/api/v1/text-to-speech/VOICE_ID \
  -H "Content-Type: application/json" \
  -H "x-proxy-secret: YOUR_SECRET" \
  -d '{"text": "Hello world", "model_id": "eleven_monolingual_v1"}' \
  --output test.mp3
```

## ⚡ 性能特点

### 优势
- **无服务器成本**: 按使用量付费
- **全球分发**: Vercel 的边缘网络
- **自动扩展**: 无需手动管理容量
- **零维护**: 无需管理服务器

### 限制
- **冷启动延迟**: 首次请求可能较慢（1-3秒）
- **执行时间限制**: 最大 30 秒（Hobby 计划为 10 秒）
- **内存限制**: 默认 1024MB
- **并发限制**: 根据计划不同

## 🔄 与 Railway 版本的兼容性

### API 接口完全兼容
- 相同的路由结构
- 相同的请求/响应格式
- 相同的错误处理逻辑
- 相同的安全认证机制

### 配置迁移
只需要更改 Cloudflare Worker 中的代理 URL：
```javascript
// 从 Railway
const PROXY_URL = 'https://your-app.railway.app';

// 改为 Vercel
const PROXY_URL = 'https://your-app.vercel.app';
```

## 🛠️ 故障排除

### 常见问题

1. **冷启动超时**
   - 现象：首次请求失败
   - 解决：重试请求或使用预热机制

2. **请求体解析错误**
   - 现象：400 错误
   - 解决：检查 Content-Type 头

3. **CORS 错误**
   - 现象：浏览器阻止请求
   - 解决：检查 OPTIONS 请求处理

### 调试模式
设置 `NODE_ENV=development` 启用详细日志：
```bash
vercel env add NODE_ENV development
```

## 📊 监控和日志

### Vercel 控制面板
- 函数调用统计
- 错误率监控
- 响应时间分析
- 实时日志查看

### 自定义监控
所有关键操作都有详细日志：
- `[PROXY] Forwarding request to: ...`
- `[PROXY] Received response from ElevenLabs with status: ...`
- `[STREAM] Error processing stream: ...`

## 🚀 生产环境建议

1. **设置自定义域名**: 提高稳定性和品牌一致性
2. **配置环境变量**: 使用 Vercel 的安全环境变量管理
3. **监控设置**: 配置告警和监控
4. **备份策略**: 保持 Railway 版本作为备份

## 📝 更新日志

### v1.0.0
- 初始版本发布
- 完整的 Railway 功能移植
- Serverless 架构优化
- 流处理优化
- 完整的错误处理
