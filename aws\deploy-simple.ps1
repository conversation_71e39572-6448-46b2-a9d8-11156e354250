# AWS Lambda TTS Proxy 简化部署脚本

param(
    [string]$StackName = "tts-proxy",
    [string]$ProxySecret = "",
    [string]$Environment = "production",
    [string]$Region = ""
)

# 颜色输出函数
function Write-Info { 
    param([string]$Message) 
    Write-Host "[INFO] $Message" -ForegroundColor Cyan 
}

function Write-Success { 
    param([string]$Message) 
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green 
}

function Write-ErrorMsg { 
    param([string]$Message) 
    Write-Host "[ERROR] $Message" -ForegroundColor Red 
}

function Write-Warning { 
    param([string]$Message) 
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow 
}

# 检查前置条件
function Test-Prerequisites {
    Write-Info "检查部署前置条件..."
    
    # 检查 AWS CLI
    $awsVersion = $null
    try {
        $awsVersion = aws --version 2>$null
    } catch {}
    
    if (-not $awsVersion) {
        Write-ErrorMsg "AWS CLI 未安装。请先安装 AWS CLI。"
        Write-Info "下载地址: https://aws.amazon.com/cli/"
        return $false
    }
    Write-Success "AWS CLI 已安装"
    
    # 检查 SAM CLI
    $samVersion = $null
    try {
        $samVersion = sam --version 2>$null
    } catch {}
    
    if (-not $samVersion) {
        Write-ErrorMsg "AWS SAM CLI 未安装。请先安装 SAM CLI。"
        Write-Info "安装命令: pip install aws-sam-cli"
        return $false
    }
    Write-Success "SAM CLI 已安装"
    
    # 检查 Node.js
    $nodeVersion = $null
    try {
        $nodeVersion = node --version 2>$null
    } catch {}
    
    if (-not $nodeVersion) {
        Write-ErrorMsg "Node.js 未安装。请先安装 Node.js 18+。"
        Write-Info "下载地址: https://nodejs.org/"
        return $false
    }
    Write-Success "Node.js 已安装: $nodeVersion"
    
    # 检查 AWS 凭证
    try {
        $identity = aws sts get-caller-identity 2>$null | ConvertFrom-Json
        if ($identity) {
            Write-Success "AWS 凭证已配置"
            Write-Info "账户ID: $($identity.Account)"
        } else {
            Write-ErrorMsg "AWS 凭证未配置。请运行 'aws configure' 配置凭证。"
            return $false
        }
    } catch {
        Write-ErrorMsg "AWS 凭证验证失败。请运行 'aws configure' 配置凭证。"
        return $false
    }
    
    return $true
}

# 获取用户输入
function Get-UserInput {
    Write-Info "配置部署参数..."
    
    # 获取堆栈名称
    if (-not $StackName) {
        $input = Read-Host "请输入堆栈名称 (默认: tts-proxy)"
        if ($input) { $script:StackName = $input } else { $script:StackName = "tts-proxy" }
    }
    
    # 获取代理密钥
    while (-not $ProxySecret -or $ProxySecret.Length -lt 8) {
        $ProxySecret = Read-Host "请输入代理密钥 (至少8位字符)" -AsSecureString
        $ProxySecret = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($ProxySecret))
        if ($ProxySecret.Length -lt 8) {
            Write-ErrorMsg "代理密钥长度必须至少8位字符！"
        }
    }
    $script:ProxySecret = $ProxySecret
    
    # 获取环境
    if (-not $Environment) {
        $input = Read-Host "请选择环境 (development/production, 默认: production)"
        if ($input) { $script:Environment = $input } else { $script:Environment = "production" }
    }
    
    # 获取AWS区域
    if (-not $Region) {
        try {
            $currentRegion = aws configure get region 2>$null
            if (-not $currentRegion) { $currentRegion = "us-east-1" }
            $input = Read-Host "请确认AWS区域 (当前: $currentRegion)"
            if ($input) { $script:Region = $input } else { $script:Region = $currentRegion }
        } catch {
            $input = Read-Host "请输入AWS区域 (默认: us-east-1)"
            if ($input) { $script:Region = $input } else { $script:Region = "us-east-1" }
        }
    }
    
    Write-Success "部署参数配置完成！"
    Write-Info "堆栈名称: $StackName"
    Write-Info "环境: $Environment"
    Write-Info "AWS区域: $Region"
}

# 构建和部署
function Deploy-Application {
    Write-Info "开始构建和部署..."
    
    # 安装依赖
    if (Test-Path "package.json") {
        Write-Info "安装 Node.js 依赖..."
        npm install --production
        if ($LASTEXITCODE -ne 0) {
            Write-ErrorMsg "npm install 失败"
            return $false
        }
    }
    
    # SAM 构建
    Write-Info "使用 SAM 构建应用..."
    sam build
    if ($LASTEXITCODE -ne 0) {
        Write-ErrorMsg "SAM build 失败"
        return $false
    }
    
    # 检查堆栈是否存在
    $stackExists = $false
    try {
        aws cloudformation describe-stacks --stack-name $StackName --region $Region 2>$null | Out-Null
        $stackExists = $true
    } catch {
        $stackExists = $false
    }
    
    # 部署
    Write-Info "部署应用到 AWS..."
    if (-not $stackExists) {
        Write-Info "首次部署，使用引导模式..."
        sam deploy --guided --stack-name $StackName --region $Region --parameter-overrides "ProxySecret=$ProxySecret" "Environment=$Environment" --capabilities CAPABILITY_IAM
    } else {
        Write-Info "更新现有堆栈..."
        sam deploy --stack-name $StackName --region $Region --parameter-overrides "ProxySecret=$ProxySecret" "Environment=$Environment" --capabilities CAPABILITY_IAM --no-confirm-changeset
    }
    
    if ($LASTEXITCODE -ne 0) {
        Write-ErrorMsg "部署失败"
        return $false
    }
    
    Write-Success "部署完成！"
    return $true
}

# 获取部署信息
function Get-DeploymentInfo {
    Write-Info "获取部署信息..."
    
    try {
        $apiUrl = aws cloudformation describe-stacks --stack-name $StackName --region $Region --query 'Stacks[0].Outputs[?OutputKey==`TtsProxyApiUrl`].OutputValue' --output text 2>$null
        $healthUrl = aws cloudformation describe-stacks --stack-name $StackName --region $Region --query 'Stacks[0].Outputs[?OutputKey==`HealthEndpoint`].OutputValue' --output text 2>$null
        
        Write-Success "部署信息："
        Write-Host "================================================" -ForegroundColor White
        Write-Host "🌐 API Gateway URL: $apiUrl" -ForegroundColor Cyan
        Write-Host "❤️  健康检查端点: $healthUrl" -ForegroundColor Green
        Write-Host "🎤 TTS端点: $apiUrl/api/v1/text-to-speech/{voice_id}" -ForegroundColor Yellow
        Write-Host "================================================" -ForegroundColor White
        
        # 生成配置文件
        $configContent = @"
# AWS Lambda TTS Proxy 配置信息
# 生成时间: $(Get-Date)

API_BASE_URL=$apiUrl
HEALTH_ENDPOINT=$healthUrl
TTS_ENDPOINT=$apiUrl/api/v1/text-to-speech/{voice_id}
PROXY_SECRET=$ProxySecret
STACK_NAME=$StackName
REGION=$Region
ENVIRONMENT=$Environment
"@
        
        $configContent | Out-File -FilePath "deployment-config.txt" -Encoding UTF8
        Write-Info "配置信息已保存到: deployment-config.txt"
        
        return $true
    } catch {
        Write-Warning "无法获取部署信息，请检查堆栈状态"
        return $false
    }
}

# 主函数
function Main {
    Write-Info "开始 AWS Lambda TTS Proxy 部署流程..."
    Write-Host "================================================" -ForegroundColor Green
    
    # 检查前置条件
    if (-not (Test-Prerequisites)) {
        Write-ErrorMsg "前置条件检查失败，请解决问题后重试"
        exit 1
    }
    
    # 获取用户输入
    Get-UserInput
    
    # 部署应用
    if (-not (Deploy-Application)) {
        Write-ErrorMsg "部署失败，请检查错误信息"
        exit 1
    }
    
    # 获取部署信息
    Get-DeploymentInfo | Out-Null
    
    Write-Success "🎉 部署完成！"
    Write-Info "请保存上述端点信息，并确保在客户端请求中包含正确的 x-proxy-secret 头。"
}

# 运行主函数
Main
