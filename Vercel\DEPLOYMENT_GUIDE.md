# Vercel 快速部署指南

## 🚀 一键部署

### 方法 1: Vercel CLI 部署（推荐）

```bash
# 1. 安装 Vercel CLI
npm i -g vercel

# 2. 进入项目目录
cd Vercel

# 3. 安装依赖
npm install

# 4. 登录 Vercel
vercel login

# 5. 部署到生产环境
vercel --prod
```

### 方法 2: GitHub 集成部署

1. 将代码推送到 GitHub 仓库
2. 在 [Vercel Dashboard](https://vercel.com/dashboard) 中导入项目
3. 选择 `Vercel` 文件夹作为根目录
4. 点击 Deploy

## ⚙️ 环境变量配置

### 必需的环境变量

```bash
# 代理认证密钥（与 Cloudflare Worker 保持一致）
PROXY_SECRET=your-secret-key-here
```

### 可选的环境变量

```bash
# 开发模式（启用详细日志）
NODE_ENV=development
```

### 配置方法

#### 方法 1: Vercel CLI
```bash
# 添加生产环境变量
vercel env add PROXY_SECRET production

# 添加开发环境变量
vercel env add NODE_ENV development
```

#### 方法 2: Vercel Dashboard
1. 进入项目设置
2. 点击 "Environment Variables"
3. 添加变量名和值
4. 选择环境（Production/Preview/Development）

## 🧪 部署后验证

### 1. 健康检查
```bash
curl https://your-app.vercel.app/api/health
```

**期望响应:**
```json
{
  "status": "healthy",
  "message": "Proxy server is up and running on Vercel",
  "timestamp": "2024-07-05T12:00:00.000Z",
  "platform": "vercel"
}
```

### 2. TTS 功能测试
```bash
# 运行测试脚本
node test-proxy.js
```

或手动测试：
```bash
curl -X POST https://your-app.vercel.app/api/v1/text-to-speech/EXAVITQu4vr4xnSDxMaL \
  -H "Content-Type: application/json" \
  -H "x-proxy-secret: your-secret" \
  -d '{"text": "Hello world", "model_id": "eleven_monolingual_v1"}' \
  --output test.mp3
```

## 🔧 Cloudflare Worker 配置更新

### 更新代理 URL

在您的 Cloudflare Worker 中，更新代理服务器 URL：

```javascript
// 原来的 Railway 配置
const PROXY_URL = 'https://your-app.railway.app';

// 更新为 Vercel 配置
const PROXY_URL = 'https://your-app.vercel.app';
```

### 环境变量同步

确保 Cloudflare Worker 和 Vercel 使用相同的 `PROXY_SECRET`：

```javascript
// Cloudflare Worker 环境变量
PROXY_SECRET = "your-secret-key-here"

// Vercel 环境变量
PROXY_SECRET = "your-secret-key-here"  // 必须完全一致
```

## 📊 监控和日志

### Vercel Dashboard 监控
1. 访问 [Vercel Dashboard](https://vercel.com/dashboard)
2. 选择您的项目
3. 查看 "Functions" 标签页
4. 监控调用次数、错误率、响应时间

### 实时日志查看
```bash
# 查看实时日志
vercel logs --follow

# 查看特定函数日志
vercel logs --follow --scope=api/health
```

### 日志格式
```
[PROXY] Forwarding request to: https://api.elevenlabs.io/v1/text-to-speech/...
[PROXY] Received response from ElevenLabs with status: 200
[STREAM] Processing audio stream...
```

## 🛠️ 故障排除

### 常见问题

#### 1. 冷启动超时
**现象**: 首次请求失败或超时
**解决方案**:
```bash
# 预热函数
curl https://your-app.vercel.app/api/health
```

#### 2. 环境变量未生效
**现象**: 认证失败或配置错误
**解决方案**:
```bash
# 检查环境变量
vercel env ls

# 重新部署
vercel --prod
```

#### 3. CORS 错误
**现象**: 浏览器阻止请求
**解决方案**: 检查请求是否包含正确的 Content-Type 头

#### 4. 音频文件损坏
**现象**: 生成的音频无法播放
**解决方案**: 检查网络连接和 ElevenLabs API 状态

### 调试模式

启用详细日志：
```bash
# 设置开发环境
vercel env add NODE_ENV development

# 重新部署
vercel --prod
```

## 📈 性能优化

### 1. 函数配置优化
在 `vercel.json` 中调整函数配置：
```json
{
  "functions": {
    "api/**/*.js": {
      "runtime": "@vercel/node",
      "maxDuration": 30
    }
  }
}
```

### 2. 预热策略
设置定期健康检查：
```bash
# 使用 cron 服务定期访问
*/5 * * * * curl https://your-app.vercel.app/api/health
```

### 3. 缓存策略
对于静态响应，添加缓存头：
```javascript
res.setHeader('Cache-Control', 's-maxage=60');
```

## 🔄 版本管理

### 部署预览版本
```bash
# 部署到预览环境
vercel

# 部署特定分支
vercel --target preview
```

### 回滚部署
```bash
# 查看部署历史
vercel ls

# 回滚到特定版本
vercel rollback [deployment-url]
```

## 📋 部署检查清单

### 部署前检查
- [ ] 代码已推送到仓库
- [ ] 环境变量已配置
- [ ] `vercel.json` 配置正确
- [ ] 依赖项已安装

### 部署后检查
- [ ] 健康检查通过
- [ ] TTS 功能正常
- [ ] 错误处理正确
- [ ] 日志记录正常
- [ ] Cloudflare Worker 已更新代理 URL

### 生产环境检查
- [ ] 自定义域名配置（可选）
- [ ] SSL 证书正常
- [ ] 监控告警设置
- [ ] 备份策略确认

## 🎉 部署完成

恭喜！您的 Vercel TTS 代理服务器已成功部署。

### 下一步
1. 更新 Cloudflare Worker 的代理 URL
2. 进行端到端测试
3. 设置监控和告警
4. 考虑设置自定义域名

### 获取帮助
- [Vercel 文档](https://vercel.com/docs)
- [项目 README](./README.md)
- [架构对比分析](./ARCHITECTURE_COMPARISON.md)
