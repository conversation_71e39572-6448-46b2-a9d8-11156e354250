# AWS Lambda TTS Proxy 部署脚本

param(
    [string]$StackName = "tts-proxy",
    [string]$ProxySecret = "",
    [string]$Environment = "production", 
    [string]$Region = ""
)

Write-Host "================================================" -ForegroundColor Green
Write-Host "AWS Lambda TTS Proxy 部署脚本" -ForegroundColor Green
Write-Host "================================================" -ForegroundColor Green

# 检查 AWS CLI
Write-Host "[INFO] 检查 AWS CLI..." -ForegroundColor Cyan
try {
    $awsVersion = aws --version 2>$null
    if ($awsVersion) {
        Write-Host "[SUCCESS] AWS CLI 已安装" -ForegroundColor Green
    } else {
        Write-Host "[ERROR] AWS CLI 未安装" -ForegroundColor Red
        Write-Host "请从 https://aws.amazon.com/cli/ 下载安装" -ForegroundColor Yellow
        exit 1
    }
} catch {
    Write-Host "[ERROR] AWS CLI 检查失败" -ForegroundColor Red
    exit 1
}

# 检查 SAM CLI
Write-Host "[INFO] 检查 SAM CLI..." -ForegroundColor Cyan
try {
    $samVersion = sam --version 2>$null
    if ($samVersion) {
        Write-Host "[SUCCESS] SAM CLI 已安装" -ForegroundColor Green
    } else {
        Write-Host "[ERROR] SAM CLI 未安装" -ForegroundColor Red
        Write-Host "请运行: pip install aws-sam-cli" -ForegroundColor Yellow
        exit 1
    }
} catch {
    Write-Host "[ERROR] SAM CLI 检查失败" -ForegroundColor Red
    exit 1
}

# 检查 Node.js
Write-Host "[INFO] 检查 Node.js..." -ForegroundColor Cyan
try {
    $nodeVersion = node --version 2>$null
    if ($nodeVersion) {
        Write-Host "[SUCCESS] Node.js 已安装: $nodeVersion" -ForegroundColor Green
    } else {
        Write-Host "[ERROR] Node.js 未安装" -ForegroundColor Red
        Write-Host "请从 https://nodejs.org/ 下载安装" -ForegroundColor Yellow
        exit 1
    }
} catch {
    Write-Host "[ERROR] Node.js 检查失败" -ForegroundColor Red
    exit 1
}

# 检查 AWS 凭证
Write-Host "[INFO] 检查 AWS 凭证..." -ForegroundColor Cyan
try {
    $identity = aws sts get-caller-identity 2>$null | ConvertFrom-Json
    if ($identity) {
        Write-Host "[SUCCESS] AWS 凭证已配置" -ForegroundColor Green
        Write-Host "账户ID: $($identity.Account)" -ForegroundColor Cyan
    } else {
        Write-Host "[ERROR] AWS 凭证未配置" -ForegroundColor Red
        Write-Host "请运行: aws configure" -ForegroundColor Yellow
        exit 1
    }
} catch {
    Write-Host "[ERROR] AWS 凭证验证失败" -ForegroundColor Red
    Write-Host "请运行: aws configure" -ForegroundColor Yellow
    exit 1
}

Write-Host "`n[SUCCESS] 前置条件检查通过!" -ForegroundColor Green

# 获取用户输入
Write-Host "`n[INFO] 配置部署参数..." -ForegroundColor Cyan

# 堆栈名称
if (-not $StackName) {
    $input = Read-Host "请输入堆栈名称 (默认: tts-proxy)"
    if ($input) { $StackName = $input } else { $StackName = "tts-proxy" }
}

# 代理密钥
while (-not $ProxySecret -or $ProxySecret.Length -lt 8) {
    $ProxySecret = Read-Host "请输入代理密钥 (至少8位字符)" -AsSecureString
    $ProxySecret = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($ProxySecret))
    if ($ProxySecret.Length -lt 8) {
        Write-Host "[ERROR] 代理密钥长度必须至少8位字符!" -ForegroundColor Red
    }
}

# 环境
if (-not $Environment) {
    $input = Read-Host "请选择环境 (development/production, 默认: production)"
    if ($input) { $Environment = $input } else { $Environment = "production" }
}

# AWS区域
if (-not $Region) {
    try {
        $currentRegion = aws configure get region 2>$null
        if (-not $currentRegion) { $currentRegion = "us-east-1" }
        $input = Read-Host "请确认AWS区域 (当前: $currentRegion)"
        if ($input) { $Region = $input } else { $Region = $currentRegion }
    } catch {
        $input = Read-Host "请输入AWS区域 (默认: us-east-1)"
        if ($input) { $Region = $input } else { $Region = "us-east-1" }
    }
}

Write-Host "`n[SUCCESS] 部署参数配置完成!" -ForegroundColor Green
Write-Host "堆栈名称: $StackName" -ForegroundColor Cyan
Write-Host "环境: $Environment" -ForegroundColor Cyan
Write-Host "AWS区域: $Region" -ForegroundColor Cyan

# 安装依赖
Write-Host "`n[INFO] 安装 Node.js 依赖..." -ForegroundColor Cyan
if (Test-Path "package.json") {
    npm install --production
    if ($LASTEXITCODE -ne 0) {
        Write-Host "[ERROR] npm install 失败" -ForegroundColor Red
        exit 1
    }
    Write-Host "[SUCCESS] 依赖安装完成" -ForegroundColor Green
}

# SAM 构建
Write-Host "`n[INFO] 使用 SAM 构建应用..." -ForegroundColor Cyan
sam build
if ($LASTEXITCODE -ne 0) {
    Write-Host "[ERROR] SAM build 失败" -ForegroundColor Red
    exit 1
}
Write-Host "[SUCCESS] 应用构建完成" -ForegroundColor Green

# 检查堆栈是否存在
Write-Host "`n[INFO] 检查现有堆栈..." -ForegroundColor Cyan
$stackExists = $false
try {
    aws cloudformation describe-stacks --stack-name $StackName --region $Region 2>$null | Out-Null
    $stackExists = $true
    Write-Host "[INFO] 发现现有堆栈，将进行更新" -ForegroundColor Yellow
} catch {
    Write-Host "[INFO] 未发现现有堆栈，将进行首次部署" -ForegroundColor Cyan
}

# 部署
Write-Host "`n[INFO] 部署应用到 AWS..." -ForegroundColor Cyan
if (-not $stackExists) {
    Write-Host "[INFO] 首次部署，使用引导模式..." -ForegroundColor Cyan
    sam deploy --guided --stack-name $StackName --region $Region --parameter-overrides "ProxySecret=$ProxySecret" "Environment=$Environment" --capabilities CAPABILITY_IAM
} else {
    Write-Host "[INFO] 更新现有堆栈..." -ForegroundColor Cyan
    sam deploy --stack-name $StackName --region $Region --parameter-overrides "ProxySecret=$ProxySecret" "Environment=$Environment" --capabilities CAPABILITY_IAM --no-confirm-changeset
}

if ($LASTEXITCODE -ne 0) {
    Write-Host "[ERROR] 部署失败" -ForegroundColor Red
    exit 1
}

Write-Host "`n[SUCCESS] 部署完成!" -ForegroundColor Green

# 获取部署信息
Write-Host "`n[INFO] 获取部署信息..." -ForegroundColor Cyan
try {
    $apiUrl = aws cloudformation describe-stacks --stack-name $StackName --region $Region --query 'Stacks[0].Outputs[?OutputKey==`TtsProxyApiUrl`].OutputValue' --output text 2>$null
    $healthUrl = aws cloudformation describe-stacks --stack-name $StackName --region $Region --query 'Stacks[0].Outputs[?OutputKey==`HealthEndpoint`].OutputValue' --output text 2>$null
    
    Write-Host "`n================================================" -ForegroundColor White
    Write-Host "部署信息:" -ForegroundColor Green
    Write-Host "================================================" -ForegroundColor White
    Write-Host "API Gateway URL: $apiUrl" -ForegroundColor Cyan
    Write-Host "健康检查端点: $healthUrl" -ForegroundColor Green
    Write-Host "TTS端点: $apiUrl/api/v1/text-to-speech/{voice_id}" -ForegroundColor Yellow
    Write-Host "================================================" -ForegroundColor White
    
    # 生成配置文件
    $configLines = @(
        "# AWS Lambda TTS Proxy 配置信息",
        "# 生成时间: $(Get-Date)",
        "",
        "API_BASE_URL=$apiUrl",
        "HEALTH_ENDPOINT=$healthUrl", 
        "TTS_ENDPOINT=$apiUrl/api/v1/text-to-speech/{voice_id}",
        "PROXY_SECRET=$ProxySecret",
        "STACK_NAME=$StackName",
        "REGION=$Region",
        "ENVIRONMENT=$Environment"
    )
    
    $configLines | Out-File -FilePath "deployment-config.txt" -Encoding UTF8
    Write-Host "`n[INFO] 配置信息已保存到: deployment-config.txt" -ForegroundColor Cyan
    
} catch {
    Write-Host "[WARNING] 无法获取部署信息，请检查堆栈状态" -ForegroundColor Yellow
}

Write-Host "`n[SUCCESS] 部署完成!" -ForegroundColor Green
Write-Host "请保存上述端点信息，并确保在客户端请求中包含正确的 x-proxy-secret 头。" -ForegroundColor Cyan
