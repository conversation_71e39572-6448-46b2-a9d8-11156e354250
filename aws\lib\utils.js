import fetch from 'node-fetch';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, GetCommand, PutCommand, UpdateCommand } from '@aws-sdk/lib-dynamodb';

// DynamoDB 客户端初始化
const dynamoClient = new DynamoDBClient({ region: process.env.AWS_REGION || 'us-east-1' });
const docClient = DynamoDBDocumentClient.from(dynamoClient);

// 速率限制配置
const RATE_LIMIT_TABLE = process.env.RATE_LIMIT_TABLE || 'tts-rate-limits';
const RATE_LIMIT_WINDOW = 60000; // 1分钟
const MAX_REQUESTS_PER_WINDOW = 30; // 每分钟最多30个请求

// 【AWS Lambda适配】DynamoDB速率限制检查 - 暂时禁用
export async function checkRateLimit(clientId) {
  // 暂时注释掉速率限制功能，直接允许所有请求通过
  console.log(`[RATE_LIMIT] 速率限制已禁用 - 客户端: ${clientId}`);
  return { allowed: true };

  /* 原始速率限制逻辑 - 暂时注释
  try {
    const now = Date.now();
    const windowStart = now - RATE_LIMIT_WINDOW;
    const itemKey = `client_${clientId}`;

    // 获取当前客户端的请求记录
    const getCommand = new GetCommand({
      TableName: RATE_LIMIT_TABLE,
      Key: { clientId: itemKey }
    });

    let currentRecord;
    try {
      const result = await docClient.send(getCommand);
      currentRecord = result.Item;
    } catch (error) {
      console.warn('[RATE_LIMIT] DynamoDB get error:', error.message);
      // 如果DynamoDB不可用，允许请求通过（降级策略）
      return { allowed: true };
    }

    // 清理过期的时间戳
    let timestamps = [];
    if (currentRecord && currentRecord.timestamps) {
      timestamps = currentRecord.timestamps.filter(t => t > windowStart);
    }

    // 检查是否超过限制
    if (timestamps.length >= MAX_REQUESTS_PER_WINDOW) {
      return {
        allowed: false,
        error: { error: 'Rate limit exceeded: Too many requests' }
      };
    }

    // 添加当前请求时间戳
    timestamps.push(now);

    // 更新DynamoDB记录
    const putCommand = new PutCommand({
      TableName: RATE_LIMIT_TABLE,
      Item: {
        clientId: itemKey,
        timestamps: timestamps,
        lastUpdated: now,
        ttl: Math.floor((now + RATE_LIMIT_WINDOW * 2) / 1000) // 2分钟后自动过期
      }
    });

    try {
      await docClient.send(putCommand);
    } catch (error) {
      console.warn('[RATE_LIMIT] DynamoDB put error:', error.message);
      // 如果写入失败，仍然允许请求（降级策略）
    }

    return { allowed: true };

  } catch (error) {
    console.error('[RATE_LIMIT] Unexpected error:', error);
    // 发生错误时允许请求通过（降级策略）
    return { allowed: true };
  }
  */
}

// 【AWS Lambda适配】代理密钥验证函数
export function checkProxySecret(headers) {
  // Lambda中headers的key可能是小写的
  const incomingSecret = headers['x-proxy-secret'] || headers['X-Proxy-Secret'];

  // 【安全检查1】环境变量必须存在
  if (!process.env.PROXY_SECRET) {
    console.error('[SECURITY] PROXY_SECRET environment variable is not set!');
    return {
      isValid: false,
      error: { error: 'Server configuration error: Missing proxy secret' }
    };
  }

  // 【安全检查2】请求头必须存在
  if (!incomingSecret) {
    console.warn('[SECURITY] Request missing x-proxy-secret header');
    return {
      isValid: false,
      error: { error: 'Unauthorized: Missing proxy secret header' }
    };
  }

  // 【安全检查3】密钥长度检查（防止空字符串）
  if (incomingSecret.length < 8) {
    console.warn('[SECURITY] Proxy secret too short');
    return {
      isValid: false,
      error: { error: 'Unauthorized: Invalid proxy secret format' }
    };
  }

  // 调试日志（仅开发环境）
  if (process.env.NODE_ENV === 'development') {
    console.log('--- SECRET DEBUG ---');
    console.log(`Expected Secret (from env): >${process.env.PROXY_SECRET}<`);
    console.log(`Received Secret (from header): >${incomingSecret}<`);
    console.log('Are they identical?:', process.env.PROXY_SECRET === incomingSecret);
    console.log('--- END DEBUG ---');
  }

  // 【安全检查4】密钥比较（使用严格相等）
  if (incomingSecret !== process.env.PROXY_SECRET) {
    console.warn('[SECURITY] Invalid proxy secret provided');
    return {
      isValid: false,
      error: { error: 'Unauthorized: Invalid proxy secret' }
    };
  }

  // 【安全日志】记录成功的认证（生产环境）
  if (process.env.NODE_ENV === 'production') {
    console.log('[SECURITY] ✅ Proxy secret validation successful');
  }

  return { isValid: true };
}

// 【AWS Lambda适配】CORS头处理
export function getCorsHeaders() {
  return {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, x-proxy-secret'
  };
}

// 【AWS Lambda适配】OPTIONS请求处理
export function handleOptions() {
  return {
    statusCode: 204,
    headers: getCorsHeaders(),
    body: ''
  };
}

// 【AWS Lambda适配】错误响应处理
export function createErrorResponse(statusCode, error) {
  return {
    statusCode,
    headers: {
      'Content-Type': 'application/json',
      ...getCorsHeaders()
    },
    body: JSON.stringify(error)
  };
}

// 【AWS Lambda适配】成功响应处理
export function createSuccessResponse(data) {
  return {
    statusCode: 200,
    headers: {
      'Content-Type': 'application/json',
      ...getCorsHeaders()
    },
    body: JSON.stringify(data)
  };
}

// 【AWS Lambda适配】二进制响应处理
export function createBinaryResponse(buffer, contentType) {
  return {
    statusCode: 200,
    headers: {
      'Content-Type': contentType,
      ...getCorsHeaders()
    },
    body: buffer.toString('base64'),
    isBase64Encoded: true
  };
}

// ElevenLabs API 调用（与Vercel版本相同）
export async function callElevenLabsAPI(voiceId, requestBody, timeout = 30000) {
  const elevenLabsUrl = `https://api.elevenlabs.io/v1/text-to-speech/${voiceId}?allow_unauthenticated=1`;
  
  console.log(`[PROXY] Forwarding request to: ${elevenLabsUrl}`);

  const elevenLabsResponse = await fetch(elevenLabsUrl, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: requestBody,
    timeout: timeout,
  });

  console.log(`[PROXY] Received response from ElevenLabs with status: ${elevenLabsResponse.status}`);
  
  return elevenLabsResponse;
}
