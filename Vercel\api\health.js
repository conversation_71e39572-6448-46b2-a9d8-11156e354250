import { checkProxySecret, setCorsHeaders, handleOptions, sendError, sendSuccess } from '../lib/utils.js';

export default async function handler(req, res) {
  // 处理 CORS 预检请求
  if (req.method === 'OPTIONS') {
    return handleOptions(res);
  }

  // 只允许 GET 请求
  if (req.method !== 'GET') {
    return sendError(res, 405, { error: 'Method not allowed' });
  }

  try {
    // 【安全验证】检查代理密钥
    const authResult = checkProxySecret(req);
    if (!authResult.isValid) {
      return sendError(res, 401, authResult.error);
    }

    // 返回健康状态
    return sendSuccess(res, {
      status: 'healthy',
      message: 'Proxy server is up and running on Vercel',
      timestamp: new Date().toISOString(),
      platform: 'vercel'
    });

  } catch (error) {
    console.error('[HEALTH] Error:', error);
    return sendError(res, 500, {
      error: 'Internal server error',
      details: error.message
    });
  }
}
