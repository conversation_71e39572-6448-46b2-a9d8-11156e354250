import {
  checkProxySecret,
  checkRateLimit,
  handleOptions,
  createErrorResponse,
  createBinaryResponse,
  callElevenLabsAPI
} from '../lib/utils.js';

// 【AWS Lambda适配】文本转语音函数
export const handler = async (event, context) => {
  console.log('[TTS] Lambda function invoked');
  console.log('[TTS] Event:', JSON.stringify(event, null, 2));

  try {
    // 获取HTTP方法 - 兼容v1.0和v2.0 payload format
    const httpMethod = event.httpMethod || event.requestContext?.http?.method;

    // 处理 CORS 预检请求
    if (httpMethod === 'OPTIONS') {
      return handleOptions();
    }

    // 只允许 POST 请求
    if (httpMethod !== 'POST') {
      return createErrorResponse(405, { error: 'Method not allowed' });
    }

    // 从路径参数获取 voice_id
    const voiceId = event.pathParameters?.voice_id || event.pathParameters?.proxy;
    
    if (!voiceId) {
      return createErrorResponse(400, { error: 'Voice ID is required' });
    }

    console.log(`[TTS] Processing request for voice_id: ${voiceId}`);

    // 【安全验证】检查代理密钥
    const authResult = checkProxySecret(event.headers);
    if (!authResult.isValid) {
      return createErrorResponse(401, authResult.error);
    }

    // 【安全验证】速率限制检查
    // 从多个可能的来源获取客户端IP
    const clientId = event.requestContext?.identity?.sourceIp || 
                    event.headers['x-forwarded-for']?.split(',')[0] || 
                    event.headers['X-Forwarded-For']?.split(',')[0] ||
                    'unknown';
    
    console.log(`[TTS] Client ID for rate limiting: ${clientId}`);
    
    const rateLimitResult = await checkRateLimit(clientId);
    if (!rateLimitResult.allowed) {
      console.warn(`[SECURITY] Rate limit exceeded for client: ${clientId}`);
      return createErrorResponse(429, rateLimitResult.error);
    }

    // 获取请求体
    let requestBody;
    if (event.body) {
      // API Gateway 传递的请求体
      if (event.isBase64Encoded) {
        // 如果是Base64编码的，先解码
        requestBody = Buffer.from(event.body, 'base64').toString();
      } else {
        requestBody = event.body;
      }
    } else {
      return createErrorResponse(400, { error: 'Request body is required' });
    }

    console.log('[TTS] Request body received, length:', requestBody.length);

    // 调用 ElevenLabs API
    const elevenLabsResponse = await callElevenLabsAPI(
      voiceId, 
      requestBody, 
      30000 // 30秒超时
    );

    // 处理响应
    if (elevenLabsResponse.ok) {
      // 成功响应 - 处理音频数据
      console.log('[TTS] ElevenLabs API call successful, processing audio...');
      
      // 【关键修改】Lambda必须等待完整下载，不能流式传输
      const audioBuffer = await elevenLabsResponse.arrayBuffer();
      const buffer = Buffer.from(audioBuffer);
      
      console.log(`[TTS] Audio buffer received, size: ${buffer.length} bytes`);
      
      // 检查响应大小（Lambda有6MB限制）
      if (buffer.length > 5 * 1024 * 1024) { // 5MB警告阈值
        console.warn(`[TTS] Large response size: ${buffer.length} bytes`);
      }
      
      const contentType = elevenLabsResponse.headers.get('Content-Type') || 'audio/mpeg';
      
      return createBinaryResponse(buffer, contentType);

    } else {
      // 错误响应 - 转发 ElevenLabs 的错误
      let errorBody;
      try {
        const errorText = await elevenLabsResponse.text();
        errorBody = JSON.parse(errorText);
      } catch (parseError) {
        errorBody = { error: 'Failed to parse ElevenLabs error response' };
      }
      
      console.error('[TTS] Error from ElevenLabs:', JSON.stringify(errorBody));
      
      return createErrorResponse(elevenLabsResponse.status, errorBody);
    }

  } catch (error) {
    console.error('[TTS] Internal error:', error);
    
    // 处理不同类型的错误
    if (error.name === 'AbortError' || error.message.includes('timeout')) {
      return createErrorResponse(504, {
        error: 'Gateway Timeout: Request to ElevenLabs timed out',
        details: error.message
      });
    }
    
    return createErrorResponse(502, {
      error: 'Bad Gateway: The proxy server encountered an internal error.',
      details: error.message
    });
  }
};
