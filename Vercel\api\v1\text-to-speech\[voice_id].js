import {
  checkProxySecret,
  checkRateLimit,
  setCorsHeaders,
  handleOptions,
  sendError,
  handleStreamResponse,
  callElevenLabsAPI
} from '../../../lib/utils.js';

export default async function handler(req, res) {
  // 处理 CORS 预检请求
  if (req.method === 'OPTIONS') {
    return handleOptions(res);
  }

  // 只允许 POST 请求
  if (req.method !== 'POST') {
    return sendError(res, 405, { error: 'Method not allowed' });
  }

  try {
    const { voice_id } = req.query;

    if (!voice_id) {
      return sendError(res, 400, { error: 'Voice ID is required' });
    }

    // 【安全验证】检查代理密钥 - 这是关键的安全检查！
    const authResult = checkProxySecret(req);
    if (!authResult.isValid) {
      return sendError(res, 401, authResult.error);
    }

    // 【安全验证】速率限制检查（使用 IP 地址作为客户端标识）
    const clientId = req.headers['x-forwarded-for'] || req.connection?.remoteAddress || 'unknown';
    const rateLimitResult = checkRateLimit(clientId);
    if (!rateLimitResult.allowed) {
      console.warn(`[SECURITY] Rate limit exceeded for client: ${clientId}`);
      return sendError(res, 429, rateLimitResult.error);
    }

    // 获取请求体
    let requestBody;
    if (req.body) {
      // Vercel 已经解析了请求体
      requestBody = JSON.stringify(req.body);
    } else {
      // 手动读取请求体
      const chunks = [];
      for await (const chunk of req) {
        chunks.push(chunk);
      }
      requestBody = Buffer.concat(chunks);
    }

    // 调用 ElevenLabs API
    const elevenLabsResponse = await callElevenLabsAPI(
      voice_id, 
      requestBody, 
      30000 // Vercel 的超时限制更严格，使用 30 秒
    );

    // 处理响应
    if (elevenLabsResponse.ok) {
      // 成功响应 - 处理音频流
      const streamSuccess = await handleStreamResponse(elevenLabsResponse, res);
      
      if (!streamSuccess) {
        return sendError(res, 500, {
          error: 'Failed to process audio stream',
          details: 'Stream processing error'
        });
      }
    } else {
      // 错误响应 - 转发 ElevenLabs 的错误
      const errorBody = await elevenLabsResponse.json().catch(() => ({}));
      
      console.error('[PROXY] Error from ElevenLabs:', JSON.stringify(errorBody));
      
      setCorsHeaders(res);
      res.status(elevenLabsResponse.status).json(errorBody);
    }

  } catch (error) {
    console.error('[PROXY] Internal fetch error:', error);
    
    // 处理不同类型的错误
    if (error.name === 'AbortError' || error.message.includes('timeout')) {
      return sendError(res, 504, {
        error: 'Gateway Timeout: Request to ElevenLabs timed out',
        details: error.message
      });
    }
    
    return sendError(res, 502, {
      error: 'Bad Gateway: The proxy server encountered an internal error.',
      details: error.message
    });
  }
}
