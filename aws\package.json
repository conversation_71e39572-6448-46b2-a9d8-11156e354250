{"name": "aws-lambda-tts-proxy", "version": "1.0.0", "description": "AWS Lambda serverless proxy for ElevenLabs TTS API", "type": "module", "main": "index.js", "scripts": {"build": "zip -r lambda-deployment.zip . -x '*.git*' 'node_modules/.cache/*' 'test/*'", "deploy": "aws lambda update-function-code --function-name tts-proxy --zip-file fileb://lambda-deployment.zip", "test": "node test/test-local.js"}, "dependencies": {"node-fetch": "^3.3.2", "@aws-sdk/client-dynamodb": "^3.0.0", "@aws-sdk/lib-dynamodb": "^3.0.0"}, "engines": {"node": ">=18.0.0"}, "author": "", "license": "ISC"}