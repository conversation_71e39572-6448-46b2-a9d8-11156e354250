# AWS Lambda TTS Proxy

这是原Vercel TTS代理服务的AWS Lambda版本，提供相同的功能但适配了AWS Lambda的运行环境。

## 🏗️ 架构对比

| 组件 | Vercel版本 | AWS Lambda版本 |
|------|------------|----------------|
| **函数托管** | Vercel Functions | AWS Lambda |
| **API路由** | vercel.json | API Gateway |
| **函数签名** | `(req, res)` | `(event, context)` |
| **状态存储** | 内存Map | DynamoDB |
| **响应处理** | 流式传输 | Base64编码 |
| **部署方式** | Git推送 | SAM/CloudFormation |

## 📁 项目结构

```
aws/
├── functions/              # Lambda函数
│   ├── health.js          # 健康检查函数
│   └── text-to-speech.js  # TTS代理函数
├── lib/                   # 共享工具库
│   └── utils.js          # 工具函数（适配Lambda）
├── template.yaml         # SAM模板
├── package.json          # 依赖配置
├── deploy.sh            # 部署脚本
└── README.md           # 说明文档
```

## 🚀 快速部署

### 前置要求

1. **AWS CLI** - [安装指南](https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html)
2. **AWS SAM CLI** - [安装指南](https://docs.aws.amazon.com/serverless-application-model/latest/developerguide/install-sam-cli.html)
3. **Node.js 18+** - [下载地址](https://nodejs.org/)

### 配置AWS凭证

```bash
aws configure
```

### 一键部署

#### **Windows系统**

**方式1: PowerShell脚本 (推荐)**
```powershell
# 在PowerShell中运行
.\deploy.ps1
```

**方式2: 批处理脚本**
```cmd
# 在命令提示符中运行
deploy.bat
```

#### **Linux/macOS系统**
```bash
# 给部署脚本执行权限
chmod +x deploy.sh

# 运行部署脚本
./deploy.sh
```

部署脚本会引导你完成：
- 堆栈名称配置
- 代理密钥设置
- 环境选择
- AWS区域确认

### 手动部署

如果你更喜欢手动控制部署过程：

```bash
# 1. 安装依赖
npm install

# 2. 构建应用
sam build

# 3. 部署应用
sam deploy --guided
```

## 🔧 配置说明

### 环境变量

| 变量名 | 说明 | 必需 |
|--------|------|------|
| `PROXY_SECRET` | 代理访问密钥 | ✅ |
| `NODE_ENV` | 运行环境 | ✅ |
| `RATE_LIMIT_TABLE` | DynamoDB表名 | ✅ |
| `AWS_REGION` | AWS区域 | ✅ |

### DynamoDB表结构

速率限制使用DynamoDB存储，表结构：

```json
{
  "clientId": "client_192.168.1.1",
  "timestamps": [1640995200000, 1640995260000],
  "lastUpdated": 1640995260000,
  "ttl": **********
}
```

## 📡 API端点

部署完成后，你将获得以下端点：

### 健康检查
```
GET https://your-api-id.execute-api.region.amazonaws.com/v1/health
Headers: x-proxy-secret: your-secret
```

### 文本转语音
```
POST https://your-api-id.execute-api.region.amazonaws.com/v1/api/v1/text-to-speech/{voice_id}
Headers: 
  Content-Type: application/json
  x-proxy-secret: your-secret
Body: {"text": "Hello world", "model_id": "eleven_monolingual_v1"}
```

## 🔒 安全特性

### 1. 代理密钥验证
- 所有请求必须包含 `x-proxy-secret` 头
- 密钥长度至少8位字符
- 支持开发环境调试日志

### 2. 速率限制
- 使用DynamoDB存储请求计数
- 每分钟最多30个请求（可配置）
- 基于客户端IP地址限制
- 自动清理过期记录

### 3. CORS支持
- 支持跨域请求
- 预检请求处理
- 安全头配置

## 🔄 与Vercel版本的差异

### 主要变化

1. **函数签名**
   ```javascript
   // Vercel
   export default async function handler(req, res) { ... }
   
   // Lambda
   export const handler = async (event, context) => { ... }
   ```

2. **请求处理**
   ```javascript
   // Vercel
   const { voice_id } = req.query;
   const secret = req.headers['x-proxy-secret'];
   
   // Lambda
   const { voice_id } = event.pathParameters;
   const secret = event.headers['x-proxy-secret'];
   ```

3. **响应格式**
   ```javascript
   // Vercel
   res.status(200).json(data);
   
   // Lambda
   return {
     statusCode: 200,
     headers: { 'Content-Type': 'application/json' },
     body: JSON.stringify(data)
   };
   ```

4. **二进制响应**
   ```javascript
   // Vercel - 流式传输
   res.write(buffer);
   res.end();
   
   // Lambda - Base64编码
   return {
     statusCode: 200,
     body: buffer.toString('base64'),
     isBase64Encoded: true
   };
   ```

### 速率限制重构

Vercel版本使用内存Map，Lambda版本使用DynamoDB：

```javascript
// Vercel - 内存存储（不可靠）
const requestCounts = new Map();

// Lambda - DynamoDB存储（可靠）
const docClient = DynamoDBDocumentClient.from(dynamoClient);
```

## 📊 监控和日志

### CloudWatch日志

Lambda函数的日志会自动发送到CloudWatch：

```bash
# 查看健康检查函数日志
aws logs tail /aws/lambda/your-stack-health --follow

# 查看TTS函数日志
aws logs tail /aws/lambda/your-stack-tts --follow
```

### 监控指标

在CloudWatch中可以监控：
- 函数调用次数
- 错误率
- 执行时间
- 内存使用
- DynamoDB读写次数

## 🛠️ 故障排除

### 常见问题

1. **部署失败**
   - 检查AWS凭证配置
   - 确认IAM权限
   - 查看CloudFormation事件

2. **函数超时**
   - 增加函数超时时间
   - 检查ElevenLabs API响应时间
   - 优化代码性能

3. **DynamoDB错误**
   - 检查表是否存在
   - 确认IAM权限
   - 查看DynamoDB指标

4. **CORS错误**
   - 检查API Gateway CORS配置
   - 确认预检请求处理
   - 验证响应头设置

### 调试技巧

1. **本地测试**
   ```bash
   sam local start-api
   ```

2. **查看详细日志**
   ```bash
   sam logs -n HealthFunction --stack-name your-stack --tail
   ```

3. **测试单个函数**
   ```bash
   sam local invoke HealthFunction -e events/health-event.json
   ```

## 🔄 更新和维护

### 更新代码

```bash
# 修改代码后重新部署
sam build && sam deploy
```

### 更新配置

```bash
# 更新环境变量或其他配置
sam deploy --parameter-overrides ProxySecret=new-secret
```

### 删除堆栈

```bash
aws cloudformation delete-stack --stack-name your-stack-name
```

## 💰 成本估算

AWS Lambda按使用量计费：

- **请求费用**: $0.20 per 1M requests
- **计算费用**: $0.********** per GB-second
- **DynamoDB**: 按读写请求计费
- **API Gateway**: $3.50 per million API calls

对于中等使用量（10万次请求/月），预计成本约$5-10/月。

## 📞 支持

如果遇到问题：

1. 查看CloudWatch日志
2. 检查AWS文档
3. 参考SAM CLI文档
4. 查看项目Issues

---

**注意**: 这个Lambda版本与原Vercel版本功能完全一致，只是适配了AWS的运行环境。核心的ElevenLabs API调用逻辑保持不变。
