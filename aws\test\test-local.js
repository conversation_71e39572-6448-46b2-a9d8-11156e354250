#!/usr/bin/env node

/**
 * AWS Lambda TTS Proxy 本地测试脚本
 * 用于在部署前测试函数逻辑
 */

import { handler as healthHandler } from '../functions/health.js';
import { handler as ttsHandler } from '../functions/text-to-speech.js';

// 模拟环境变量
process.env.PROXY_SECRET = 'test-secret-12345';
process.env.NODE_ENV = 'development';
process.env.AWS_REGION = 'us-east-1';
process.env.RATE_LIMIT_TABLE = 'test-rate-limits';

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 模拟Lambda事件和上下文
function createMockEvent(method, path, headers = {}, body = null, pathParameters = {}) {
  return {
    httpMethod: method, // v1.0 format
    path: path,
    headers: {
      'Content-Type': 'application/json',
      ...headers
    },
    pathParameters: pathParameters,
    body: body,
    isBase64Encoded: false,
    requestContext: {
      http: {
        method: method // v2.0 format
      },
      identity: {
        sourceIp: '127.0.0.1'
      }
    }
  };
}

function createMockContext() {
  return {
    functionName: 'test-function',
    awsRequestId: 'test-request-id-' + Date.now(),
    getRemainingTimeInMillis: () => 30000
  };
}

// 测试健康检查函数
async function testHealthFunction() {
  log('\n=== 测试健康检查函数 ===', 'cyan');
  
  try {
    // 测试1: 正常GET请求
    log('\n1. 测试正常GET请求...', 'blue');
    const event1 = createMockEvent('GET', '/health', {
      'x-proxy-secret': 'test-secret-12345'
    });
    const context1 = createMockContext();
    
    const result1 = await healthHandler(event1, context1);
    log(`状态码: ${result1.statusCode}`, result1.statusCode === 200 ? 'green' : 'red');
    log(`响应: ${result1.body}`, 'yellow');
    
    // 测试2: 缺少密钥
    log('\n2. 测试缺少密钥...', 'blue');
    const event2 = createMockEvent('GET', '/health');
    const context2 = createMockContext();
    
    const result2 = await healthHandler(event2, context2);
    log(`状态码: ${result2.statusCode}`, result2.statusCode === 401 ? 'green' : 'red');
    log(`响应: ${result2.body}`, 'yellow');
    
    // 测试3: 错误的HTTP方法
    log('\n3. 测试错误的HTTP方法...', 'blue');
    const event3 = createMockEvent('POST', '/health', {
      'x-proxy-secret': 'test-secret-12345'
    });
    const context3 = createMockContext();
    
    const result3 = await healthHandler(event3, context3);
    log(`状态码: ${result3.statusCode}`, result3.statusCode === 405 ? 'green' : 'red');
    log(`响应: ${result3.body}`, 'yellow');
    
    // 测试4: OPTIONS请求
    log('\n4. 测试OPTIONS请求...', 'blue');
    const event4 = createMockEvent('OPTIONS', '/health');
    const context4 = createMockContext();
    
    const result4 = await healthHandler(event4, context4);
    log(`状态码: ${result4.statusCode}`, result4.statusCode === 204 ? 'green' : 'red');
    log(`CORS头: ${JSON.stringify(result4.headers)}`, 'yellow');
    
  } catch (error) {
    log(`健康检查测试失败: ${error.message}`, 'red');
    console.error(error);
  }
}

// 测试TTS函数（模拟，不实际调用ElevenLabs）
async function testTtsFunction() {
  log('\n=== 测试TTS函数 ===', 'cyan');
  
  try {
    // 测试1: 缺少voice_id
    log('\n1. 测试缺少voice_id...', 'blue');
    const event1 = createMockEvent('POST', '/api/v1/text-to-speech/', {
      'x-proxy-secret': 'test-secret-12345'
    }, JSON.stringify({ text: 'Hello world' }));
    const context1 = createMockContext();
    
    const result1 = await ttsHandler(event1, context1);
    log(`状态码: ${result1.statusCode}`, result1.statusCode === 400 ? 'green' : 'red');
    log(`响应: ${result1.body}`, 'yellow');
    
    // 测试2: 缺少密钥
    log('\n2. 测试缺少密钥...', 'blue');
    const event2 = createMockEvent('POST', '/api/v1/text-to-speech/voice123', {}, 
      JSON.stringify({ text: 'Hello world' }), { voice_id: 'voice123' });
    const context2 = createMockContext();
    
    const result2 = await ttsHandler(event2, context2);
    log(`状态码: ${result2.statusCode}`, result2.statusCode === 401 ? 'green' : 'red');
    log(`响应: ${result2.body}`, 'yellow');
    
    // 测试3: 缺少请求体
    log('\n3. 测试缺少请求体...', 'blue');
    const event3 = createMockEvent('POST', '/api/v1/text-to-speech/voice123', {
      'x-proxy-secret': 'test-secret-12345'
    }, null, { voice_id: 'voice123' });
    const context3 = createMockContext();
    
    const result3 = await ttsHandler(event3, context3);
    log(`状态码: ${result3.statusCode}`, result3.statusCode === 400 ? 'green' : 'red');
    log(`响应: ${result3.body}`, 'yellow');
    
    // 测试4: OPTIONS请求
    log('\n4. 测试OPTIONS请求...', 'blue');
    const event4 = createMockEvent('OPTIONS', '/api/v1/text-to-speech/voice123', {}, null, { voice_id: 'voice123' });
    const context4 = createMockContext();
    
    const result4 = await ttsHandler(event4, context4);
    log(`状态码: ${result4.statusCode}`, result4.statusCode === 204 ? 'green' : 'red');
    log(`CORS头: ${JSON.stringify(result4.headers)}`, 'yellow');
    
    // 注意: 我们不测试实际的ElevenLabs API调用，因为那需要真实的网络请求
    log('\n注意: 实际的ElevenLabs API调用测试需要在部署后进行', 'magenta');
    
  } catch (error) {
    log(`TTS函数测试失败: ${error.message}`, 'red');
    console.error(error);
  }
}

// 测试工具函数
async function testUtilFunctions() {
  log('\n=== 测试工具函数 ===', 'cyan');
  
  try {
    // 动态导入工具函数
    const { checkProxySecret, getCorsHeaders, createErrorResponse } = await import('../lib/utils.js');
    
    // 测试1: 代理密钥验证
    log('\n1. 测试代理密钥验证...', 'blue');
    
    const validHeaders = { 'x-proxy-secret': 'test-secret-12345' };
    const invalidHeaders = { 'x-proxy-secret': 'wrong' };
    const missingHeaders = {};
    
    const validResult = checkProxySecret(validHeaders);
    const invalidResult = checkProxySecret(invalidHeaders);
    const missingResult = checkProxySecret(missingHeaders);
    
    log(`有效密钥: ${validResult.isValid ? '✅' : '❌'}`, validResult.isValid ? 'green' : 'red');
    log(`无效密钥: ${!invalidResult.isValid ? '✅' : '❌'}`, !invalidResult.isValid ? 'green' : 'red');
    log(`缺少密钥: ${!missingResult.isValid ? '✅' : '❌'}`, !missingResult.isValid ? 'green' : 'red');
    
    // 测试2: CORS头
    log('\n2. 测试CORS头...', 'blue');
    const corsHeaders = getCorsHeaders();
    log(`CORS头: ${JSON.stringify(corsHeaders, null, 2)}`, 'yellow');
    
    // 测试3: 错误响应
    log('\n3. 测试错误响应格式...', 'blue');
    const errorResponse = createErrorResponse(400, { error: 'Test error' });
    log(`错误响应: ${JSON.stringify(errorResponse, null, 2)}`, 'yellow');
    
  } catch (error) {
    log(`工具函数测试失败: ${error.message}`, 'red');
    console.error(error);
  }
}

// 主测试函数
async function runTests() {
  log('🧪 开始AWS Lambda TTS Proxy本地测试', 'green');
  log('================================================', 'green');
  
  await testUtilFunctions();
  await testHealthFunction();
  await testTtsFunction();
  
  log('\n================================================', 'green');
  log('✅ 本地测试完成！', 'green');
  log('\n📝 测试说明:', 'cyan');
  log('- 这些测试验证了函数的基本逻辑和错误处理', 'yellow');
  log('- 实际的ElevenLabs API调用需要在部署后测试', 'yellow');
  log('- DynamoDB速率限制功能需要真实的DynamoDB表', 'yellow');
  log('\n🚀 下一步: 运行 ./deploy.sh 进行实际部署', 'cyan');
}

// 运行测试
runTests().catch(error => {
  log(`测试运行失败: ${error.message}`, 'red');
  console.error(error);
  process.exit(1);
});
