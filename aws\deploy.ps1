# AWS Lambda TTS Proxy Windows PowerShell 部署脚本
# 使用 AWS SAM (Serverless Application Model) 进行部署

param(
    [string]$StackName = "",
    [string]$ProxySecret = "",
    [string]$Environment = "production",
    [string]$Region = ""
)

# 设置错误处理
$ErrorActionPreference = "Continue"

# 颜色输出函数
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] $Message" -ForegroundColor $Color
}

function Write-Success { param([string]$Message) Write-ColorOutput $Message "Green" }
function Write-ErrorMsg { param([string]$Message) Write-ColorOutput $Message "Red" }
function Write-Warning { param([string]$Message) Write-ColorOutput $Message "Yellow" }
function Write-Info { param([string]$Message) Write-ColorOutput $Message "Cyan" }

# 检查必要的工具
function Test-Prerequisites {
    Write-Info "检查部署前置条件..."
    
    # 检查 AWS CLI
    try {
        $awsVersion = aws --version 2>$null
        if (-not $awsVersion) { throw "AWS CLI not found" }
        Write-Success "✅ AWS CLI: $($awsVersion.Split()[0])"
    }
    catch {
        Write-ErrorMsg "❌ AWS CLI 未安装。请先安装 AWS CLI。"
        Write-Info "下载地址: https://aws.amazon.com/cli/"
        exit 1
    }
    
    # 检查 SAM CLI
    try {
        $samVersion = sam --version 2>$null
        if (-not $samVersion) { throw "SAM CLI not found" }
        Write-Success "✅ SAM CLI: $samVersion"
    }
    catch {
        Write-ErrorMsg "❌ AWS SAM CLI 未安装。请先安装 SAM CLI。"
        Write-Info "安装命令: pip install aws-sam-cli"
        Write-Info "或下载MSI: https://github.com/aws/aws-sam-cli/releases"
        exit 1
    }
    
    # 检查 Node.js
    try {
        $nodeVersion = node --version 2>$null
        if (-not $nodeVersion) { throw "Node.js not found" }
        Write-Success "✅ Node.js: $nodeVersion"
    }
    catch {
        Write-ErrorMsg "❌ Node.js 未安装。请先安装 Node.js 18+。"
        Write-Info "下载地址: https://nodejs.org/"
        exit 1
    }
    
    # 检查 AWS 凭证
    try {
        $identity = aws sts get-caller-identity 2>$null | ConvertFrom-Json
        Write-Success "✅ AWS 凭证已配置"
        Write-Info "账户ID: $($identity.Account)"
        Write-Info "用户: $($identity.Arn)"
    }
    catch {
        Write-ErrorMsg "❌ AWS 凭证未配置。请运行 'aws configure' 配置凭证。"
        exit 1
    }
    
    Write-Success "前置条件检查通过！"
}

# 获取用户输入
function Get-DeploymentParams {
    Write-Info "配置部署参数..."
    
    # 获取堆栈名称
    if (-not $StackName) {
        $StackName = Read-Host "请输入堆栈名称 (默认: tts-proxy)"
        if (-not $StackName) { $StackName = "tts-proxy" }
    }
    
    # 获取代理密钥
    if (-not $ProxySecret) {
        do {
            $ProxySecret = Read-Host "请输入代理密钥 (至少8位字符)" -AsSecureString
            $ProxySecret = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($ProxySecret))
            if ($ProxySecret.Length -lt 8) {
                Write-Error "代理密钥长度必须至少8位字符！"
            }
        } while ($ProxySecret.Length -lt 8)
    }
    
    # 获取环境
    if (-not $Environment) {
        $Environment = Read-Host "请选择环境 (development/production, 默认: production)"
        if (-not $Environment) { $Environment = "production" }
    }
    
    # 获取AWS区域
    if (-not $Region) {
        try {
            $Region = aws configure get region
            if (-not $Region) { $Region = "us-east-1" }
            $newRegion = Read-Host "请确认AWS区域 (当前: $Region)"
            if ($newRegion) { $Region = $newRegion }
        }
        catch {
            $Region = Read-Host "请输入AWS区域 (默认: us-east-1)"
            if (-not $Region) { $Region = "us-east-1" }
        }
    }
    
    Write-Success "部署参数配置完成！"
    Write-Info "堆栈名称: $StackName"
    Write-Info "环境: $Environment"
    Write-Info "AWS区域: $Region"
    
    return @{
        StackName = $StackName
        ProxySecret = $ProxySecret
        Environment = $Environment
        Region = $Region
    }
}

# 构建应用
function Build-Application {
    Write-Info "构建应用..."
    
    # 安装依赖
    if (Test-Path "package.json") {
        Write-Info "安装 Node.js 依赖..."
        npm install --production
        if ($LASTEXITCODE -ne 0) {
            Write-ErrorMsg "npm install 失败"
            exit 1
        }
    }
    
    # 使用 SAM 构建
    Write-Info "使用 SAM 构建应用..."
    sam build
    if ($LASTEXITCODE -ne 0) {
        Write-ErrorMsg "SAM build 失败"
        exit 1
    }
    
    Write-Success "应用构建完成！"
}

# 部署应用
function Deploy-Application {
    param($params)
    
    Write-Info "部署应用到 AWS..."
    
    # 检查堆栈是否存在
    $stackExists = $false
    try {
        aws cloudformation describe-stacks --stack-name $params.StackName --region $params.Region 2>$null | Out-Null
        $stackExists = $true
    }
    catch {
        $stackExists = $false
    }
    
    if (-not $stackExists) {
        Write-Info "首次部署，使用引导模式..."
        sam deploy --guided --stack-name $params.StackName --region $params.Region --parameter-overrides "ProxySecret=$($params.ProxySecret)" "Environment=$($params.Environment)" --capabilities CAPABILITY_IAM --confirm-changeset
    }
    else {
        Write-Info "更新现有堆栈..."
        sam deploy --stack-name $params.StackName --region $params.Region --parameter-overrides "ProxySecret=$($params.ProxySecret)" "Environment=$($params.Environment)" --capabilities CAPABILITY_IAM --no-confirm-changeset
    }
    
    if ($LASTEXITCODE -ne 0) {
        Write-ErrorMsg "部署失败"
        exit 1
    }
    
    Write-Success "应用部署完成！"
}

# 获取部署信息
function Get-DeploymentInfo {
    param($params)
    
    Write-Info "获取部署信息..."
    
    try {
        # 获取 API Gateway URL
        $apiUrl = aws cloudformation describe-stacks --stack-name $params.StackName --region $params.Region --query 'Stacks[0].Outputs[?OutputKey==`TtsProxyApiUrl`].OutputValue' --output text
        
        # 获取健康检查端点
        $healthUrl = aws cloudformation describe-stacks --stack-name $params.StackName --region $params.Region --query 'Stacks[0].Outputs[?OutputKey==`HealthEndpoint`].OutputValue' --output text
        
        Write-Success "部署信息："
        Write-Host "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━" -ForegroundColor White
        Write-Host "🌐 API Gateway URL: $apiUrl" -ForegroundColor Cyan
        Write-Host "❤️  健康检查端点: $healthUrl" -ForegroundColor Green
        Write-Host "🎤 TTS端点: $apiUrl/api/v1/text-to-speech/{voice_id}" -ForegroundColor Yellow
        Write-Host "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━" -ForegroundColor White
        
        return @{
            ApiUrl = $apiUrl
            HealthUrl = $healthUrl
        }
    }
    catch {
        Write-Warning "无法获取部署信息，请检查堆栈状态"
        return $null
    }
}

# 测试部署
function Test-Deployment {
    param($params, $endpoints)
    
    if (-not $endpoints -or -not $endpoints.HealthUrl) {
        Write-Warning "跳过部署测试 - 无法获取端点信息"
        return
    }
    
    Write-Info "测试部署..."
    
    try {
        Write-Info "测试健康检查端点..."
        
        # 使用 Invoke-RestMethod 测试健康检查
        $headers = @{
            "x-proxy-secret" = $params.ProxySecret
        }
        
        $response = Invoke-RestMethod -Uri $endpoints.HealthUrl -Method Get -Headers $headers -TimeoutSec 30
        
        if ($response.status -eq "healthy") {
            Write-Success "✅ 健康检查测试通过！"
            Write-Info "平台: $($response.platform)"
            Write-Info "区域: $($response.region)"
        }
        else {
            Write-Warning "⚠️ 健康检查响应异常"
        }
    }
    catch {
        Write-Warning "⚠️ 健康检查测试失败: $($_.Exception.Message)"
        Write-Info "这可能是正常的，请手动验证端点"
    }
}

# 主函数
function Main {
    Write-Info "开始 AWS Lambda TTS Proxy 部署流程..."
    Write-Host "================================================" -ForegroundColor Green
    
    try {
        Test-Prerequisites
        $params = Get-DeploymentParams
        Build-Application
        Deploy-Application $params
        $endpoints = Get-DeploymentInfo $params
        Test-Deployment $params $endpoints
        
        Write-Success "🎉 部署完成！"
        Write-Info "请保存上述端点信息，并确保在客户端请求中包含正确的 x-proxy-secret 头。"
        
        # 生成配置文件
        $configContent = @"
# AWS Lambda TTS Proxy 配置信息
# 生成时间: $(Get-Date)

API_BASE_URL=$($endpoints.ApiUrl)
HEALTH_ENDPOINT=$($endpoints.HealthUrl)
TTS_ENDPOINT=$($endpoints.ApiUrl)/api/v1/text-to-speech/{voice_id}
PROXY_SECRET=$($params.ProxySecret)
STACK_NAME=$($params.StackName)
REGION=$($params.Region)
ENVIRONMENT=$($params.Environment)
"@
        
        $configContent | Out-File -FilePath "deployment-config.txt" -Encoding UTF8
        Write-Info "配置信息已保存到: deployment-config.txt"
        
    }
    catch {
        Write-ErrorMsg "部署失败: $($_.Exception.Message)"
        Write-Info "请检查错误信息并重试"
        exit 1
    }
}

# 运行主函数
Main
