AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: 'TTS Proxy Service on AWS Lambda with API Gateway'

Parameters:
  ProxySecret:
    Type: String
    Description: 'Secret key for proxy authentication'
    NoEcho: true
    MinLength: 8

  Environment:
    Type: String
    Default: 'production'
    AllowedValues: ['development', 'production']
    Description: 'Deployment environment'

Globals:
  Function:
    Timeout: 30
    Runtime: nodejs18.x
    MemorySize: 512
    Environment:
      Variables:
        NODE_ENV: !Ref Environment
        PROXY_SECRET: !Ref ProxySecret
        RATE_LIMIT_TABLE: !Ref RateLimitTable

Resources:
  # DynamoDB表用于速率限制
  RateLimitTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub '${AWS::StackName}-rate-limits'
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: clientId
          AttributeType: S
      KeySchema:
        - AttributeName: clientId
          KeyType: HASH
      TimeToLiveSpecification:
        AttributeName: ttl
        Enabled: true
      Tags:
        - Key: Service
          Value: TTS-Proxy

  # API Gateway
  TtsProxyApi:
    Type: AWS::Serverless::Api
    Properties:
      StageName: v1
      Cors:
        AllowMethods: "'GET,POST,OPTIONS'"
        AllowHeaders: "'Content-Type,x-proxy-secret'"
        AllowOrigin: "'*'"
      GatewayResponses:
        DEFAULT_4XX:
          ResponseParameters:
            Headers:
              Access-Control-Allow-Origin: "'*'"
              Access-Control-Allow-Headers: "'Content-Type,x-proxy-secret'"
        DEFAULT_5XX:
          ResponseParameters:
            Headers:
              Access-Control-Allow-Origin: "'*'"
              Access-Control-Allow-Headers: "'Content-Type,x-proxy-secret'"

  # 健康检查函数
  HealthFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub '${AWS::StackName}-health'
      CodeUri: functions/
      Handler: health.handler
      Description: 'Health check endpoint for TTS proxy'
      Events:
        HealthApi:
          Type: Api
          Properties:
            RestApiId: !Ref TtsProxyApi
            Path: /health
            Method: GET
        HealthApiOptions:
          Type: Api
          Properties:
            RestApiId: !Ref TtsProxyApi
            Path: /health
            Method: OPTIONS

  # 文本转语音函数
  TextToSpeechFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub '${AWS::StackName}-tts'
      CodeUri: functions/
      Handler: text-to-speech.handler
      Description: 'Text-to-speech proxy to ElevenLabs API'
      Timeout: 60
      MemorySize: 1024
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref RateLimitTable
      Events:
        TtsApi:
          Type: Api
          Properties:
            RestApiId: !Ref TtsProxyApi
            Path: /api/v1/text-to-speech/{voice_id}
            Method: POST
        TtsApiOptions:
          Type: Api
          Properties:
            RestApiId: !Ref TtsProxyApi
            Path: /api/v1/text-to-speech/{voice_id}
            Method: OPTIONS
        # 兼容性路由（如果需要）
        TtsApiCompat:
          Type: Api
          Properties:
            RestApiId: !Ref TtsProxyApi
            Path: /tts/{voice_id}
            Method: POST
        TtsApiCompatOptions:
          Type: Api
          Properties:
            RestApiId: !Ref TtsProxyApi
            Path: /tts/{voice_id}
            Method: OPTIONS

Outputs:
  TtsProxyApiUrl:
    Description: 'API Gateway endpoint URL for TTS Proxy'
    Value: !Sub 'https://${TtsProxyApi}.execute-api.${AWS::Region}.amazonaws.com/v1'
    Export:
      Name: !Sub '${AWS::StackName}-ApiUrl'

  HealthEndpoint:
    Description: 'Health check endpoint'
    Value: !Sub 'https://${TtsProxyApi}.execute-api.${AWS::Region}.amazonaws.com/v1/health'

  TtsEndpoint:
    Description: 'Text-to-speech endpoint'
    Value: !Sub 'https://${TtsProxyApi}.execute-api.${AWS::Region}.amazonaws.com/v1/api/v1/text-to-speech/{voice_id}'

  RateLimitTableName:
    Description: 'DynamoDB table name for rate limiting'
    Value: !Ref RateLimitTable
    Export:
      Name: !Sub '${AWS::StackName}-RateLimitTable'
