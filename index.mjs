// 导入两个业务逻辑的处理器
import { handler as healthHandler } from './functions/health.js';
import { handler as ttsHandler } from './functions/text-to-speech.js';

export const handler = async (event, context) => {
  // 从 API Gateway 传来的 event 对象中获取路径和方法
  const httpMethod = event.httpMethod || event.requestContext?.http?.method;
  const path = event.path || event.rawPath;

  console.log(`[ROUTER] Received ${httpMethod} request for path: ${path}`);

  // 路由逻辑
  // 注意：这里的路径是 API Gateway 暴露给外部的路径
  if (path === '/api/v1/health' && httpMethod === 'GET') {
    console.log('[ROUTER] Routing to health handler.');
    return healthHandler(event, context);
  } 
  
  if (path.startsWith('/api/v1/text-to-speech/') && httpMethod === 'POST') {
    console.log('[ROUTER] Routing to TTS handler.');
    return ttsHandler(event, context);
  }

  // 为两个路径都添加 OPTIONS 方法的处理（为了CORS）
  if (httpMethod === 'OPTIONS' && (path === '/api/v1/health' || path.startsWith('/api/v1/text-to-speech/'))) {
      console.log('[ROUTER] Routing OPTIONS request.');
      // 我们可以复用任意一个 handler 里的 options 处理逻辑，因为它们是一样的
      return healthHandler(event, context); 
  }

  // 如果没有匹配的路由，返回 404 Not Found
  console.log(`[ROUTER] No route matched for ${httpMethod} ${path}.`);
  return {
    statusCode: 404,
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ error: 'Not Found', details: `The requested path ${path} was not found on this server.` })
  };
};