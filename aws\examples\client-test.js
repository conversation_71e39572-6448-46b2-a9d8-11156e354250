#!/usr/bin/env node

/**
 * AWS Lambda TTS Proxy 客户端测试示例
 * 用于测试部署后的API端点
 */

import fetch from 'node-fetch';

// 配置 - 部署后替换为实际值
const CONFIG = {
  // 替换为你的API Gateway URL
  baseUrl: 'https://your-api-id.execute-api.us-east-1.amazonaws.com/v1',
  // 替换为你的代理密钥
  proxySecret: 'your-proxy-secret-here',
  // 测试用的voice_id
  voiceId: 'EXAVITQu4vr4xnSDxMaL' // Bella (ElevenLabs默认声音)
};

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 测试健康检查端点
async function testHealthCheck() {
  log('\n=== 测试健康检查端点 ===', 'cyan');
  
  try {
    const response = await fetch(`${CONFIG.baseUrl}/health`, {
      method: 'GET',
      headers: {
        'x-proxy-secret': CONFIG.proxySecret
      }
    });
    
    const data = await response.json();
    
    log(`状态码: ${response.status}`, response.ok ? 'green' : 'red');
    log(`响应数据:`, 'yellow');
    console.log(JSON.stringify(data, null, 2));
    
    if (response.ok) {
      log('✅ 健康检查通过！', 'green');
      return true;
    } else {
      log('❌ 健康检查失败！', 'red');
      return false;
    }
    
  } catch (error) {
    log(`❌ 健康检查请求失败: ${error.message}`, 'red');
    return false;
  }
}

// 测试TTS端点
async function testTextToSpeech() {
  log('\n=== 测试文本转语音端点 ===', 'cyan');
  
  try {
    const requestBody = {
      text: "Hello! This is a test of the AWS Lambda TTS proxy service.",
      model_id: "eleven_monolingual_v1",
      voice_settings: {
        stability: 0.5,
        similarity_boost: 0.5
      }
    };
    
    log(`发送请求到: ${CONFIG.baseUrl}/api/v1/text-to-speech/${CONFIG.voiceId}`, 'blue');
    log(`请求体: ${JSON.stringify(requestBody, null, 2)}`, 'yellow');
    
    const startTime = Date.now();
    
    const response = await fetch(`${CONFIG.baseUrl}/api/v1/text-to-speech/${CONFIG.voiceId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-proxy-secret': CONFIG.proxySecret
      },
      body: JSON.stringify(requestBody)
    });
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    log(`状态码: ${response.status}`, response.ok ? 'green' : 'red');
    log(`响应时间: ${duration}ms`, 'blue');
    log(`Content-Type: ${response.headers.get('Content-Type')}`, 'yellow');
    
    if (response.ok) {
      // 检查是否是音频响应
      const contentType = response.headers.get('Content-Type');
      if (contentType && contentType.includes('audio')) {
        const audioBuffer = await response.arrayBuffer();
        log(`✅ 成功获取音频数据，大小: ${audioBuffer.byteLength} bytes`, 'green');
        
        // 可选：保存音频文件
        const fs = await import('fs');
        const filename = `test-audio-${Date.now()}.mp3`;
        fs.writeFileSync(filename, Buffer.from(audioBuffer));
        log(`💾 音频已保存为: ${filename}`, 'cyan');
        
        return true;
      } else {
        log('❌ 响应不是音频格式', 'red');
        const text = await response.text();
        log(`响应内容: ${text}`, 'yellow');
        return false;
      }
    } else {
      log('❌ TTS请求失败！', 'red');
      const errorData = await response.json().catch(() => ({ error: 'Failed to parse error response' }));
      log(`错误信息: ${JSON.stringify(errorData, null, 2)}`, 'red');
      return false;
    }
    
  } catch (error) {
    log(`❌ TTS请求异常: ${error.message}`, 'red');
    return false;
  }
}

// 测试速率限制
async function testRateLimit() {
  log('\n=== 测试速率限制 ===', 'cyan');
  
  try {
    log('发送多个快速请求来测试速率限制...', 'blue');
    
    const promises = [];
    const requestCount = 5;
    
    for (let i = 0; i < requestCount; i++) {
      const promise = fetch(`${CONFIG.baseUrl}/health`, {
        method: 'GET',
        headers: {
          'x-proxy-secret': CONFIG.proxySecret
        }
      });
      promises.push(promise);
    }
    
    const responses = await Promise.all(promises);
    
    let successCount = 0;
    let rateLimitCount = 0;
    
    for (let i = 0; i < responses.length; i++) {
      const response = responses[i];
      log(`请求 ${i + 1}: 状态码 ${response.status}`, response.ok ? 'green' : 'yellow');
      
      if (response.ok) {
        successCount++;
      } else if (response.status === 429) {
        rateLimitCount++;
      }
    }
    
    log(`✅ 成功请求: ${successCount}`, 'green');
    log(`⚠️  速率限制: ${rateLimitCount}`, 'yellow');
    
    if (successCount > 0) {
      log('✅ 速率限制功能正常工作', 'green');
      return true;
    } else {
      log('❌ 所有请求都被限制，可能配置有问题', 'red');
      return false;
    }
    
  } catch (error) {
    log(`❌ 速率限制测试失败: ${error.message}`, 'red');
    return false;
  }
}

// 测试CORS
async function testCors() {
  log('\n=== 测试CORS支持 ===', 'cyan');
  
  try {
    // 发送OPTIONS预检请求
    const response = await fetch(`${CONFIG.baseUrl}/health`, {
      method: 'OPTIONS'
    });
    
    log(`OPTIONS状态码: ${response.status}`, response.status === 204 ? 'green' : 'red');
    
    const corsHeaders = {
      'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
      'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
      'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers')
    };
    
    log('CORS头信息:', 'yellow');
    console.log(JSON.stringify(corsHeaders, null, 2));
    
    if (corsHeaders['Access-Control-Allow-Origin'] === '*') {
      log('✅ CORS配置正确', 'green');
      return true;
    } else {
      log('❌ CORS配置可能有问题', 'red');
      return false;
    }
    
  } catch (error) {
    log(`❌ CORS测试失败: ${error.message}`, 'red');
    return false;
  }
}

// 主测试函数
async function runTests() {
  log('🧪 开始AWS Lambda TTS Proxy API测试', 'green');
  log('================================================', 'green');
  
  // 检查配置
  if (CONFIG.baseUrl.includes('your-api-id') || CONFIG.proxySecret === 'your-proxy-secret-here') {
    log('❌ 请先在CONFIG中配置正确的API URL和代理密钥！', 'red');
    process.exit(1);
  }
  
  log(`测试目标: ${CONFIG.baseUrl}`, 'cyan');
  log(`使用密钥: ${CONFIG.proxySecret.substring(0, 4)}****`, 'cyan');
  
  const results = [];
  
  // 运行所有测试
  results.push(await testHealthCheck());
  results.push(await testCors());
  results.push(await testRateLimit());
  results.push(await testTextToSpeech());
  
  // 汇总结果
  log('\n================================================', 'green');
  const passedTests = results.filter(r => r).length;
  const totalTests = results.length;
  
  if (passedTests === totalTests) {
    log(`🎉 所有测试通过！(${passedTests}/${totalTests})`, 'green');
  } else {
    log(`⚠️  部分测试失败 (${passedTests}/${totalTests})`, 'yellow');
  }
  
  log('\n📝 测试完成说明:', 'cyan');
  log('- 如果健康检查通过，说明基本部署成功', 'yellow');
  log('- 如果TTS测试通过，说明核心功能正常', 'yellow');
  log('- 如果速率限制测试通过，说明DynamoDB集成正常', 'yellow');
  log('- 如果CORS测试通过，说明可以从浏览器调用', 'yellow');
}

// 运行测试
runTests().catch(error => {
  log(`测试运行失败: ${error.message}`, 'red');
  console.error(error);
  process.exit(1);
});
