#!/usr/bin/env node

/**
 * Vercel TTS Proxy 测试脚本
 * 用于验证部署后的代理服务器功能
 */

import fetch from 'node-fetch';
import fs from 'fs';

// 配置
const CONFIG = {
  // 替换为你的 Vercel 应用 URL
  BASE_URL: process.env.VERCEL_URL || 'https://your-app.vercel.app',
  // 替换为你的代理密钥
  PROXY_SECRET: process.env.PROXY_SECRET || 'your-proxy-secret',
  // 测试用的语音 ID
  VOICE_ID: 'EXAVITQu4vr4xnSDxMaL', // Bella 的语音 ID
};

console.log('🧪 开始测试 Vercel TTS Proxy...\n');

// 测试 1: 健康检查（无认证 - 应该失败）
async function testHealthWithoutAuth() {
  console.log('📋 测试 1: 健康检查（无认证 - 应该失败）');
  try {
    const response = await fetch(`${CONFIG.BASE_URL}/api/health`);
    const data = await response.json();

    if (response.status === 401) {
      console.log('✅ 安全验证正常（正确拒绝无认证请求）');
      console.log(`   状态码: ${response.status}`);
      console.log(`   错误: ${data.error}`);
    } else {
      console.log('❌ 安全漏洞！无认证请求被允许');
      console.log(`   状态码: ${response.status}`);
      console.log(`   响应: ${JSON.stringify(data)}`);
    }
  } catch (error) {
    console.log('❌ 健康检查异常');
    console.log(`   错误: ${error.message}`);
  }
  console.log('');
}

// 测试 1.5: 健康检查（带认证 - 应该成功）
async function testHealthWithAuth() {
  console.log('📋 测试 1.5: 健康检查（带认证 - 应该成功）');
  try {
    const response = await fetch(`${CONFIG.BASE_URL}/api/health`, {
      headers: {
        'x-proxy-secret': CONFIG.PROXY_SECRET,
      }
    });
    const data = await response.json();

    if (response.ok) {
      console.log('✅ 认证健康检查通过');
      console.log(`   状态: ${data.status}`);
      console.log(`   消息: ${data.message}`);
      console.log(`   平台: ${data.platform}`);
    } else {
      console.log('❌ 认证健康检查失败');
      console.log(`   状态码: ${response.status}`);
      console.log(`   错误: ${JSON.stringify(data)}`);
    }
  } catch (error) {
    console.log('❌ 认证健康检查异常');
    console.log(`   错误: ${error.message}`);
  }
  console.log('');
}

// 测试 2: TTS 功能（无认证）
async function testTTSWithoutAuth() {
  console.log('📋 测试 2: TTS 功能（无认证）');
  try {
    const response = await fetch(
      `${CONFIG.BASE_URL}/api/v1/text-to-speech/${CONFIG.VOICE_ID}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: 'Hello from Vercel proxy test!',
          model_id: 'eleven_monolingual_v1'
        })
      }
    );

    if (response.ok) {
      console.log('✅ TTS 请求成功（无认证模式）');
      console.log(`   状态码: ${response.status}`);
      console.log(`   Content-Type: ${response.headers.get('Content-Type')}`);
      
      // 保存音频文件
      const buffer = await response.buffer();
      fs.writeFileSync('test-output-vercel.mp3', buffer);
      console.log(`   音频已保存: test-output-vercel.mp3 (${buffer.length} bytes)`);
    } else {
      const errorData = await response.json().catch(() => ({}));
      console.log('❌ TTS 请求失败');
      console.log(`   状态码: ${response.status}`);
      console.log(`   错误: ${JSON.stringify(errorData)}`);
    }
  } catch (error) {
    console.log('❌ TTS 请求异常');
    console.log(`   错误: ${error.message}`);
  }
  console.log('');
}

// 测试 3: TTS 功能（带认证）
async function testTTSWithAuth() {
  console.log('📋 测试 3: TTS 功能（带认证）');
  try {
    const response = await fetch(
      `${CONFIG.BASE_URL}/api/v1/text-to-speech/${CONFIG.VOICE_ID}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-proxy-secret': CONFIG.PROXY_SECRET,
        },
        body: JSON.stringify({
          text: 'Hello from authenticated Vercel proxy test!',
          model_id: 'eleven_monolingual_v1'
        })
      }
    );

    if (response.ok) {
      console.log('✅ TTS 请求成功（带认证）');
      console.log(`   状态码: ${response.status}`);
      console.log(`   Content-Type: ${response.headers.get('Content-Type')}`);
      
      // 保存音频文件
      const buffer = await response.buffer();
      fs.writeFileSync('test-output-vercel-auth.mp3', buffer);
      console.log(`   音频已保存: test-output-vercel-auth.mp3 (${buffer.length} bytes)`);
    } else {
      const errorData = await response.json().catch(() => ({}));
      console.log('❌ TTS 请求失败');
      console.log(`   状态码: ${response.status}`);
      console.log(`   错误: ${JSON.stringify(errorData)}`);
    }
  } catch (error) {
    console.log('❌ TTS 请求异常');
    console.log(`   错误: ${error.message}`);
  }
  console.log('');
}

// 测试 4: 错误处理
async function testErrorHandling() {
  console.log('📋 测试 4: 错误处理');
  try {
    // 测试无效的语音 ID
    const response = await fetch(
      `${CONFIG.BASE_URL}/api/v1/text-to-speech/invalid-voice-id`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: 'This should fail',
          model_id: 'eleven_monolingual_v1'
        })
      }
    );

    const errorData = await response.json().catch(() => ({}));
    console.log(`   状态码: ${response.status}`);
    console.log(`   错误响应: ${JSON.stringify(errorData)}`);
    
    if (response.status >= 400) {
      console.log('✅ 错误处理正常（正确返回错误状态）');
    } else {
      console.log('⚠️  错误处理异常（应该返回错误状态）');
    }
  } catch (error) {
    console.log('❌ 错误处理测试异常');
    console.log(`   错误: ${error.message}`);
  }
  console.log('');
}

// 运行所有测试
async function runAllTests() {
  console.log(`🎯 测试目标: ${CONFIG.BASE_URL}`);
  console.log(`🔑 代理密钥: ${CONFIG.PROXY_SECRET ? '已配置' : '未配置'}`);
  console.log('');

  await testHealthWithoutAuth();
  await testHealthWithAuth();
  await testTTSWithoutAuth();
  await testTTSWithAuth();
  await testErrorHandling();

  console.log('🎉 测试完成！');
  console.log('');
  console.log('📝 注意事项：');
  console.log('1. 如果认证测试失败，请检查 PROXY_SECRET 环境变量');
  console.log('2. 如果 TTS 测试失败，请检查 ElevenLabs API 状态');
  console.log('3. 生成的音频文件可以用于验证音质');
  console.log('4. 首次请求可能因冷启动而较慢');
}

// 执行测试
runAllTests().catch(console.error);
