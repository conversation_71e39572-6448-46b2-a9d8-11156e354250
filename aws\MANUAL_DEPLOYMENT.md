# 手动部署指南 (无需CLI工具)

如果你不想安装AWS CLI和SAM CLI，可以按照以下步骤手动部署：

## 📦 第1步: 准备部署包

1. **打包代码**
```powershell
# 在aws目录下运行
Compress-Archive -Path functions,lib,package.json -DestinationPath lambda-deployment.zip
```

2. **验证包内容**
```
lambda-deployment.zip
├── functions/
│   ├── health.js
│   └── text-to-speech.js
├── lib/
│   └── utils.js
└── package.json
```

## 🌐 第2步: 创建DynamoDB表

1. 登录AWS控制台
2. 进入DynamoDB服务
3. 创建表：
   - 表名：`tts-rate-limits`
   - 分区键：`clientId` (字符串)
   - 启用TTL：`ttl`字段

## 🔧 第3步: 创建IAM角色

1. 进入IAM服务
2. 创建角色：
   - 信任实体：AWS Lambda
   - 权限策略：
     - `AWSLambdaBasicExecutionRole`
     - `AmazonDynamoDBFullAccess`

## ⚡ 第4步: 创建Lambda函数

### 健康检查函数

1. 进入Lambda服务
2. 创建函数：
   - 函数名：`tts-proxy-health`
   - 运行时：Node.js 18.x
   - 角色：选择上面创建的角色

3. 上传代码：
   - 上传lambda-deployment.zip
   - 处理程序：`functions/health.handler`

4. 环境变量：
   - `PROXY_SECRET`: 你的代理密钥
   - `NODE_ENV`: production
   - `RATE_LIMIT_TABLE`: tts-rate-limits

### TTS函数

1. 创建函数：
   - 函数名：`tts-proxy-tts`
   - 运行时：Node.js 18.x
   - 角色：选择上面创建的角色
   - 超时：60秒
   - 内存：1024MB

2. 上传代码：
   - 上传lambda-deployment.zip
   - 处理程序：`functions/text-to-speech.handler`

3. 环境变量：
   - `PROXY_SECRET`: 你的代理密钥
   - `NODE_ENV`: production
   - `RATE_LIMIT_TABLE`: tts-rate-limits

## 🌐 第5步: 创建API Gateway

1. 进入API Gateway服务
2. 创建REST API：
   - API名称：`tts-proxy-api`

3. 创建资源和方法：

### 健康检查端点
- 资源：`/health`
- 方法：GET, OPTIONS
- 集成：Lambda函数 `tts-proxy-health`

### TTS端点
- 资源：`/api/v1/text-to-speech/{voice_id}`
- 方法：POST, OPTIONS  
- 集成：Lambda函数 `tts-proxy-tts`

4. 启用CORS：
- 允许来源：*
- 允许方法：GET, POST, OPTIONS
- 允许头：Content-Type, x-proxy-secret

5. 部署API：
- 创建部署阶段：`v1`

## 🧪 第6步: 测试部署

### 健康检查测试
```powershell
$headers = @{"x-proxy-secret" = "your-secret"}
Invoke-RestMethod -Uri "https://your-api-id.execute-api.region.amazonaws.com/v1/health" -Headers $headers
```

### TTS测试
```powershell
$headers = @{
    "Content-Type" = "application/json"
    "x-proxy-secret" = "your-secret"
}
$body = @{
    text = "Hello world"
    model_id = "eleven_monolingual_v1"
} | ConvertTo-Json

Invoke-RestMethod -Uri "https://your-api-id.execute-api.region.amazonaws.com/v1/api/v1/text-to-speech/EXAVITQu4vr4xnSDxMaL" -Method Post -Headers $headers -Body $body -OutFile "test.mp3"
```

## 📝 配置记录

部署完成后，记录以下信息：

```
API_BASE_URL=https://your-api-id.execute-api.region.amazonaws.com/v1
HEALTH_ENDPOINT=https://your-api-id.execute-api.region.amazonaws.com/v1/health
TTS_ENDPOINT=https://your-api-id.execute-api.region.amazonaws.com/v1/api/v1/text-to-speech/{voice_id}
PROXY_SECRET=your-secret
```

## 💡 提示

- 确保所有资源在同一个AWS区域
- 记录API Gateway的URL
- 保存代理密钥
- 测试所有端点功能

这种手动方式虽然步骤较多，但不需要安装任何CLI工具，适合一次性部署。
