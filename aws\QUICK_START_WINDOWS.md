# Windows 快速部署指南

## 🚀 5分钟快速部署

### 第1步: 检查前置条件

在PowerShell中运行以下命令检查：

```powershell
# 检查 AWS CLI
aws --version

# 检查 SAM CLI  
sam --version

# 检查 Node.js (需要18+)
node --version

# 检查 AWS 凭证
aws sts get-caller-identity
```

如果任何命令失败，请参考 [WINDOWS_SETUP.md](WINDOWS_SETUP.md) 进行安装。

### 第2步: 配置 AWS 凭证 (如果未配置)

```powershell
aws configure
```

### 第3步: 部署应用

**选择以下任一方式：**

#### 方式A: PowerShell脚本 (推荐)
```powershell
.\deploy-simple.ps1
```

#### 方式B: 批处理脚本
```cmd
deploy.bat
```

#### 方式C: 手动部署
```powershell
npm install
sam build
sam deploy --guided
```

### 第4步: 测试部署

部署完成后，使用生成的端点测试：

```powershell
# 测试健康检查 (替换为你的实际端点和密钥)
curl -H "x-proxy-secret: your-secret" https://your-api.execute-api.us-east-1.amazonaws.com/v1/health
```

## 🔧 常见错误解决

### 错误1: PowerShell执行策略
```
无法加载文件 deploy.ps1，因为在此系统上禁止运行脚本
```

**解决:**
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### 错误2: sam命令未找到
```
'sam' 不是内部或外部命令
```

**解决:**
1. 下载SAM CLI MSI: https://github.com/aws/aws-sam-cli/releases
2. 或使用pip: `pip install aws-sam-cli`

### 错误3: AWS凭证未配置
```
Unable to locate credentials
```

**解决:**
```powershell
aws configure
```

## 📋 部署参数说明

部署时需要输入：

- **堆栈名称**: 默认 `tts-proxy`
- **代理密钥**: 至少8位字符，用于API访问验证
- **环境**: `development` 或 `production`
- **AWS区域**: 默认 `us-east-1`

## 🎯 部署成功标志

看到以下输出表示部署成功：

```
================================================
部署信息:
================================================
API Gateway URL: https://abc123.execute-api.us-east-1.amazonaws.com/v1
健康检查端点: https://abc123.execute-api.us-east-1.amazonaws.com/v1/health
TTS端点: https://abc123.execute-api.us-east-1.amazonaws.com/v1/api/v1/text-to-speech/{voice_id}
================================================
```

配置信息会自动保存到 `deployment-config.txt` 文件中。

## 🧪 验证部署

### 1. 健康检查测试
```powershell
# 替换为你的实际端点和密钥
$headers = @{"x-proxy-secret" = "your-secret"}
Invoke-RestMethod -Uri "https://your-api.execute-api.us-east-1.amazonaws.com/v1/health" -Headers $headers
```

### 2. TTS功能测试
```powershell
# 替换为你的实际端点和密钥
$headers = @{
    "Content-Type" = "application/json"
    "x-proxy-secret" = "your-secret"
}
$body = @{
    text = "Hello world"
    model_id = "eleven_monolingual_v1"
} | ConvertTo-Json

Invoke-RestMethod -Uri "https://your-api.execute-api.us-east-1.amazonaws.com/v1/api/v1/text-to-speech/EXAVITQu4vr4xnSDxMaL" -Method Post -Headers $headers -Body $body -OutFile "test.mp3"
```

## 🔄 更新部署

修改代码后重新部署：

```powershell
sam build
sam deploy
```

## 🗑️ 删除资源

如果不再需要，删除所有AWS资源：

```powershell
aws cloudformation delete-stack --stack-name tts-proxy
```

## 📞 获取帮助

- 查看详细文档: [README.md](README.md)
- Windows特定问题: [WINDOWS_SETUP.md](WINDOWS_SETUP.md)
- 部署指南: [DEPLOYMENT_GUIDE.md](DEPLOYMENT_GUIDE.md)

---

**提示**: 首次部署建议使用PowerShell脚本，它提供更好的错误处理和用户体验。
